#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
聊天处理模块
包含各种聊天功能的处理器（LLM、RAG、DATAQA、CAR、ALL、SEARCH）
"""

import asyncio
import json
from typing import AsyncGenerator, Tu<PERSON>, List
from loguru import logger

from frontend.api.client import APIClient, ResponseProcessor
from frontend.utils.helpers import TimingManager, RequestHelper, HistoryFormatter, ValidationHelper, LogHelper


class BaseChatHandler:
    """基础聊天处理器"""
    
    def __init__(self, api_client: APIClient, chat_app):
        """初始化基础聊天处理器
        
        Args:
            api_client: API客户端
            chat_app: ChatApp实例
        """
        self.api_client = api_client
        self.chat_app = chat_app
        self.response_processor = ResponseProcessor()
    
    def _validate_input(self, query: str) -> bool:
        """验证输入
        
        Args:
            query: 用户查询
            
        Returns:
            是否有效
        """
        return ValidationHelper.is_valid_query(query)
    
    def _create_timing_manager(self) -> TimingManager:
        """创建计时管理器
        
        Returns:
            计时管理器实例
        """
        return TimingManager()


class LLMChatHandler(BaseChatHandler):
    """LLM聊天处理器"""
    
    async def chat_stream(self, query: str, model_id: str, user_id: str,
                         temperature: float, top_p: float, deep_thinking: bool,
                         web_search: bool, history_display: str) -> AsyncGenerator[Tuple[str, str, str, str], None]:
        """LLM问答 - 流式输出版本

        Args:
            query: 用户查询
            model_id: 模型ID
            user_id: 用户ID
            temperature: 温度参数
            top_p: Top-p参数
            deep_thinking: 是否开启深度思考
            web_search: 是否开启联网搜索
            history_display: 历史显示

        Yields:
            (history_display, reasoning_content, content_content, first_token_time)
        """
        logger.info("LLM chat_stream被调用")
        
        if not self._validate_input(query):
            yield history_display, "", "", "0.0秒"
            return
        
        # 初始化计时管理器
        timing = self._create_timing_manager()
        timing.start_timing()
        
        # 准备请求参数
        payload = RequestHelper.create_payload(
            query=query,
            user_id=user_id,
            model_id=model_id,
            history=self.chat_app.get_conversation_history("llm"),
            stream=True,
            temperature=temperature,
            top_p=top_p,
            enable_thinking=deep_thinking,
            enable_web_search=web_search
        )
        
        # 记录日志
        logger.info(LogHelper.format_log_message(
            "llm", "请求开始", payload["msg_id"], user_id, model_id, query
        ))
        
        # 调用API并实时更新
        reasoning_content = ""
        content_content = ""
        first_token_received = False
        
        try:
            async for chunk in self.api_client.stream_api_call("llm-qa", payload):
                chunk_type = chunk.get("type", "")
                chunk_content = chunk.get("content", "")
                
                # 标记首个token已接收
                if not first_token_received and chunk_content.strip():
                    first_token_received = True
                    timing.mark_first_token()
                
                if chunk_type == "reasoning":
                    timing.start_reasoning_timing()
                    reasoning_content += chunk_content
                    # 显示首token时间，添加停止闪动标记
                    first_token_time_str = f"{timing.get_first_token_time():.2f}秒"
                    if first_token_received:
                        first_token_time_str += "✓"  # 添加标记表示已收到首token
                    yield history_display, reasoning_content, content_content, first_token_time_str
                elif chunk_type == "content":
                    timing.start_content_timing()
                    content_content += chunk_content
                    # 显示首token时间，添加停止闪动标记
                    first_token_time_str = f"{timing.get_first_token_time():.2f}秒"
                    if first_token_received:
                        first_token_time_str += "✓"  # 添加标记表示已收到首token
                    yield history_display, reasoning_content, content_content, first_token_time_str
                    
        except Exception as e:
            error_msg = f"**API调用失败:** {str(e)}"
            logger.error(LogHelper.format_log_message(
                "llm", "异常", payload["msg_id"], user_id, model_id, query, error=str(e)
            ))
            content_content = error_msg
            first_token_time_str = f"{timing.get_first_token_time():.2f}秒"
            yield history_display, reasoning_content, content_content, first_token_time_str
        
        # 更新历史对话
        self.chat_app.add_to_history("llm", query, content_content)
        
        # 更新历史显示
        new_history_entry = HistoryFormatter.format_history_entry(
            query=query,
            content=content_content,
            total_time=timing.get_total_time(),
            reasoning_time=timing.get_reasoning_time(),
            content_time=timing.get_content_time(),
            first_token_time=timing.get_first_token_time()
        )
        
        final_history = HistoryFormatter.append_to_history(history_display, new_history_entry)
        final_first_token_time_str = f"{timing.get_first_token_time():.2f}秒"
        
        logger.info(LogHelper.format_log_message(
            "llm", "请求结束", payload["msg_id"], user_id, model_id, query, content=content_content[:100]
        ))
        
        yield final_history, reasoning_content, content_content, final_first_token_time_str


class RAGChatHandler(BaseChatHandler):
    """RAG聊天处理器"""
    
    async def chat_stream(self, query: str, model_id: str, user_id: str, top_k: int,
                         top_r: int, min_score: float, mode: str, deep_thinking: bool,
                         history_display: str) -> AsyncGenerator[Tuple[str, str, str, str, str], None]:
        """RAG问答 - 流式输出版本

        Args:
            query: 用户查询
            model_id: 模型ID
            user_id: 用户ID
            top_k: 检索数量
            top_r: 重排数量
            min_score: 最小相似度
            mode: 模式（standard/creative/precise）
            deep_thinking: 是否开启深度思考
            history_display: 历史显示

        Yields:
            (history_display, formatted_reference, reasoning_content, content_content, first_token_time)
        """
        logger.info("RAG chat_stream被调用")
        
        if not self._validate_input(query):
            yield history_display, "", "", "", "0.0秒"
            return
        
        # 初始化计时管理器
        timing = self._create_timing_manager()
        timing.start_timing()
        
        # 准备请求参数
        payload = RequestHelper.create_payload(
            query=query,
            user_id=user_id,
            model_id=model_id,
            history=self.chat_app.get_conversation_history("rag"),
            stream=True,
            top_k=top_k,
            top_r=top_r,
            min_score=min_score,
            mode=mode,
            enable_thinking=deep_thinking
        )
        
        # 记录日志
        logger.info(LogHelper.format_log_message(
            "rag", "请求开始", payload["msg_id"], user_id, model_id, query, top_k=top_k
        ))
        
        # 调用API并实时更新
        reference_content = ""
        reasoning_content = ""
        content_content = ""
        
        try:
            async for chunk in self.api_client.stream_api_call("rag-qa", payload):
                chunk_type = chunk.get("type", "")
                chunk_content = chunk.get("content", "")
                
                if chunk_type == "reference":
                    timing.start_reference_timing()
                    reference_content += chunk_content
                    formatted_reference = self.response_processor.format_reference_display(reference_content)
                    first_token_time_str = f"{timing.get_first_token_time():.2f}秒"
                    yield history_display, formatted_reference, reasoning_content, content_content, first_token_time_str
                elif chunk_type == "reasoning":
                    timing.start_reasoning_timing()
                    reasoning_content += chunk_content
                    formatted_reference = self.response_processor.format_reference_display(reference_content)
                    first_token_time_str = f"{timing.get_first_token_time():.2f}秒"
                    yield history_display, formatted_reference, reasoning_content, content_content, first_token_time_str
                elif chunk_type == "content":
                    timing.start_content_timing()
                    content_content += chunk_content
                    formatted_reference = self.response_processor.format_reference_display(reference_content)
                    first_token_time_str = f"{timing.get_first_token_time():.2f}秒"
                    yield history_display, formatted_reference, reasoning_content, content_content, first_token_time_str
                    
        except Exception as e:
            error_msg = f"**API调用失败:** {str(e)}"
            logger.error(LogHelper.format_log_message(
                "rag", "异常", payload["msg_id"], user_id, model_id, query, error=str(e)
            ))
            content_content = error_msg
            formatted_reference = self.response_processor.format_reference_display(reference_content)
            first_token_time_str = f"{timing.get_first_token_time():.2f}秒"
            yield history_display, formatted_reference, reasoning_content, content_content, first_token_time_str
        
        # 更新历史对话
        self.chat_app.add_to_history("rag", query, content_content)
        
        # 更新历史显示
        new_history_entry = HistoryFormatter.format_history_entry(
            query=query,
            content=content_content,
            total_time=timing.get_total_time(),
            reference_time=timing.get_reference_time(),
            reasoning_time=timing.get_reasoning_time(),
            content_time=timing.get_content_time(),
            first_token_time=timing.get_first_token_time()
        )
        
        final_history = HistoryFormatter.append_to_history(history_display, new_history_entry)
        formatted_reference = self.response_processor.format_reference_display(reference_content)
        final_first_token_time_str = f"{timing.get_first_token_time():.2f}秒"
        
        logger.info(LogHelper.format_log_message(
            "rag", "请求结束", payload["msg_id"], user_id, model_id, query, content=content_content[:100]
        ))
        
        yield final_history, formatted_reference, reasoning_content, content_content, final_first_token_time_str


class DataQAChatHandler(BaseChatHandler):
    """DATAQA聊天处理器"""

    async def chat_stream(self, query: str, model_id: str, user_id: str, top_k: int,
                         top_r: int, min_score: float, mode: str, deep_thinking: bool,
                         history_display: str) -> AsyncGenerator[Tuple[str, str, str, str, str], None]:
        """DATAQA问答 - 流式输出版本"""
        logger.info("DATAQA chat_stream被调用")

        if not self._validate_input(query):
            yield history_display, "", "", "", "0.0秒"
            return

        timing = self._create_timing_manager()
        timing.start_timing()

        payload = RequestHelper.create_payload(
            query=query,
            user_id=user_id,
            model_id=model_id,
            history=self.chat_app.get_conversation_history("dataqa"),
            stream=True,
            top_k=top_k,
            top_r=top_r,
            min_score=min_score,
            mode=mode,
            enable_thinking=deep_thinking
        )

        logger.info(LogHelper.format_log_message(
            "dataqa", "请求开始", payload["msg_id"], user_id, model_id, query, top_k=top_k
        ))

        reference_content = ""
        reasoning_content = ""
        content_content = ""

        try:
            async for chunk in self.api_client.stream_api_call("data-qa", payload):
                chunk_type = chunk.get("type", "")
                chunk_content = chunk.get("content", "")

                if chunk_type == "reference":
                    timing.start_reference_timing()
                    reference_content += chunk_content
                    formatted_reference = self.response_processor.format_reference_display(reference_content)
                    first_token_time_str = f"{timing.get_first_token_time():.2f}秒"
                    yield history_display, formatted_reference, reasoning_content, content_content, first_token_time_str
                elif chunk_type == "reasoning":
                    timing.start_reasoning_timing()
                    reasoning_content += chunk_content
                    formatted_reference = self.response_processor.format_reference_display(reference_content)
                    first_token_time_str = f"{timing.get_first_token_time():.2f}秒"
                    yield history_display, formatted_reference, reasoning_content, content_content, first_token_time_str
                elif chunk_type == "content":
                    timing.start_content_timing()
                    content_content += chunk_content
                    formatted_reference = self.response_processor.format_reference_display(reference_content)
                    first_token_time_str = f"{timing.get_first_token_time():.2f}秒"
                    yield history_display, formatted_reference, reasoning_content, content_content, first_token_time_str

        except Exception as e:
            error_msg = f"**API调用失败:** {str(e)}"
            logger.error(LogHelper.format_log_message(
                "dataqa", "异常", payload["msg_id"], user_id, model_id, query, error=str(e)
            ))
            content_content = error_msg
            formatted_reference = self.response_processor.format_reference_display(reference_content)
            first_token_time_str = f"{timing.get_first_token_time():.2f}秒"
            yield history_display, formatted_reference, reasoning_content, content_content, first_token_time_str

        self.chat_app.add_to_history("dataqa", query, content_content)

        new_history_entry = HistoryFormatter.format_history_entry(
            query=query,
            content=content_content,
            total_time=timing.get_total_time(),
            reference_time=timing.get_reference_time(),
            reasoning_time=timing.get_reasoning_time(),
            content_time=timing.get_content_time(),
            first_token_time=timing.get_first_token_time()
        )

        final_history = HistoryFormatter.append_to_history(history_display, new_history_entry)
        formatted_reference = self.response_processor.format_reference_display(reference_content)
        final_first_token_time_str = f"{timing.get_first_token_time():.2f}秒"

        logger.info(LogHelper.format_log_message(
            "dataqa", "请求结束", payload["msg_id"], user_id, model_id, query, content=content_content[:100]
        ))

        yield final_history, formatted_reference, reasoning_content, content_content, final_first_token_time_str


class CarChatHandler(BaseChatHandler):
    """汽车知识库聊天处理器"""

    async def chat_stream(self, query: str, model_id: str, user_id: str, top_k: int,
                         top_r: int, min_score: float, mode: str, deep_thinking: bool,
                         history_display: str) -> AsyncGenerator[Tuple[str, str, str, str, str], None]:
        """汽车知识库问答 - 流式输出版本"""
        logger.info("CAR chat_stream被调用")

        if not self._validate_input(query):
            yield history_display, "", "", "", "0.0秒"
            return

        timing = self._create_timing_manager()
        timing.start_timing()

        payload = RequestHelper.create_payload(
            query=query,
            user_id=user_id,
            model_id=model_id,
            history=self.chat_app.get_conversation_history("car"),
            stream=True,
            top_k=top_k,
            top_r=top_r,
            min_score=min_score,
            mode=mode,
            knowledge="car",
            enable_thinking=deep_thinking
        )

        logger.info(LogHelper.format_log_message(
            "car", "请求开始", payload["msg_id"], user_id, model_id, query, top_k=top_k
        ))

        reference_content = ""
        reasoning_content = ""
        content_content = ""

        try:
            async for chunk in self.api_client.stream_api_call("rag-qa", payload):
                chunk_type = chunk.get("type", "")
                chunk_content = chunk.get("content", "")

                if chunk_type == "reference":
                    timing.start_reference_timing()
                    reference_content += chunk_content
                    formatted_reference = self.response_processor.format_reference_display(reference_content)
                    first_token_time_str = f"{timing.get_first_token_time():.2f}秒"
                    yield history_display, formatted_reference, reasoning_content, content_content, first_token_time_str
                elif chunk_type == "reasoning":
                    timing.start_reasoning_timing()
                    reasoning_content += chunk_content
                    formatted_reference = self.response_processor.format_reference_display(reference_content)
                    first_token_time_str = f"{timing.get_first_token_time():.2f}秒"
                    yield history_display, formatted_reference, reasoning_content, content_content, first_token_time_str
                elif chunk_type == "content":
                    timing.start_content_timing()
                    content_content += chunk_content
                    formatted_reference = self.response_processor.format_reference_display(reference_content)
                    first_token_time_str = f"{timing.get_first_token_time():.2f}秒"
                    yield history_display, formatted_reference, reasoning_content, content_content, first_token_time_str

        except Exception as e:
            error_msg = f"**API调用失败:** {str(e)}"
            logger.error(LogHelper.format_log_message(
                "car", "异常", payload["msg_id"], user_id, model_id, query, error=str(e)
            ))
            content_content = error_msg
            formatted_reference = self.response_processor.format_reference_display(reference_content)
            first_token_time_str = f"{timing.get_first_token_time():.2f}秒"
            yield history_display, formatted_reference, reasoning_content, content_content, first_token_time_str

        self.chat_app.add_to_history("car", query, content_content)

        new_history_entry = HistoryFormatter.format_history_entry(
            query=query,
            content=content_content,
            total_time=timing.get_total_time(),
            reference_time=timing.get_reference_time(),
            reasoning_time=timing.get_reasoning_time(),
            content_time=timing.get_content_time(),
            first_token_time=timing.get_first_token_time()
        )

        final_history = HistoryFormatter.append_to_history(history_display, new_history_entry)
        formatted_reference = self.response_processor.format_reference_display(reference_content)
        final_first_token_time_str = f"{timing.get_first_token_time():.2f}秒"

        logger.info(LogHelper.format_log_message(
            "car", "请求结束", payload["msg_id"], user_id, model_id, query, content=content_content[:100]
        ))

        yield final_history, formatted_reference, reasoning_content, content_content, final_first_token_time_str


class ISCChatHandler(BaseChatHandler):
    """ISC知识库聊天处理器"""

    def _format_isc_references(self, references):
        """格式化ISC参考文献为JSON字符串"""
        import json
        try:
            return json.dumps(references, ensure_ascii=False, indent=2)
        except:
            return str(references)

    async def chat_stream(self, query: str, model_id: str, user_id: str, top_k: int,
                         top_r: int, min_score: float, mode: str, deep_thinking: bool,
                         history_display: str) -> AsyncGenerator[Tuple[str, str, str, str, str], None]:
        """ISC知识库问答 - 流式输出版本"""
        logger.info("ISC知识库问答流式处理开始")

        if not self._validate_input(query):
            yield history_display, "", "", "", "0.0秒"
            return

        timing = self._create_timing_manager()
        timing.start_timing()

        payload = RequestHelper.create_payload(
            query=query,
            user_id=user_id,
            model_id=model_id,
            history=self.chat_app.get_conversation_history("isc"),
            stream=True,
            top_k=top_k,
            top_r=top_r,
            min_score=min_score,
            mode=mode,
            enable_thinking=deep_thinking
        )

        logger.info(LogHelper.format_log_message(
            "isc", "请求开始", payload["msg_id"], user_id, model_id, query, top_k=top_k
        ))

        reference_content = ""
        reasoning_content = ""
        content_content = ""

        try:
            async for chunk in self.api_client.stream_api_call("isc-qa", payload):
                chunk_type = chunk.get("type", "")
                chunk_content = chunk.get("content", "")

                if chunk_type == "reference":
                    timing.start_reference_timing()
                    reference_content += chunk_content
                    formatted_reference = self.response_processor.format_reference_display(reference_content)
                    first_token_time_str = f"{timing.get_first_token_time():.2f}秒"
                    yield history_display, formatted_reference, reasoning_content, content_content, first_token_time_str
                elif chunk_type == "references":
                    # 处理ISC模块特有的references类型
                    timing.start_reference_timing()
                    references = chunk.get("references", [])
                    if references:
                        # 格式化references为显示内容
                        formatted_refs = self._format_isc_references(references)
                        reference_content = formatted_refs
                        formatted_reference = self.response_processor.format_reference_display(reference_content)
                    else:
                        formatted_reference = ""
                    first_token_time_str = f"{timing.get_first_token_time():.2f}秒"
                    yield history_display, formatted_reference, reasoning_content, content_content, first_token_time_str
                elif chunk_type == "reasoning":
                    timing.start_reasoning_timing()
                    reasoning_content += chunk_content
                    formatted_reference = self.response_processor.format_reference_display(reference_content)
                    first_token_time_str = f"{timing.get_first_token_time():.2f}秒"
                    yield history_display, formatted_reference, reasoning_content, content_content, first_token_time_str
                elif chunk_type == "content":
                    timing.start_content_timing()
                    content_content += chunk_content
                    formatted_reference = self.response_processor.format_reference_display(reference_content)
                    first_token_time_str = f"{timing.get_first_token_time():.2f}秒"
                    yield history_display, formatted_reference, reasoning_content, content_content, first_token_time_str

        except Exception as e:
            error_msg = f"**API调用失败:** {str(e)}"
            logger.error(LogHelper.format_log_message(
                "isc", "异常", payload["msg_id"], user_id, model_id, query, error=str(e)
            ))
            content_content = error_msg
            formatted_reference = self.response_processor.format_reference_display(reference_content)
            first_token_time_str = f"{timing.get_first_token_time():.2f}秒"
            yield history_display, formatted_reference, reasoning_content, content_content, first_token_time_str

        self.chat_app.add_to_history("isc", query, content_content)

        new_history_entry = HistoryFormatter.format_history_entry(
            query=query,
            content=content_content,
            total_time=timing.get_total_time(),
            reference_time=timing.get_reference_time(),
            reasoning_time=timing.get_reasoning_time(),
            content_time=timing.get_content_time(),
            first_token_time=timing.get_first_token_time()
        )

        final_history = HistoryFormatter.append_to_history(history_display, new_history_entry)
        formatted_reference = self.response_processor.format_reference_display(reference_content)
        final_first_token_time_str = f"{timing.get_first_token_time():.2f}秒"

        logger.info(LogHelper.format_log_message(
            "isc", "请求结束", payload["msg_id"], user_id, model_id, query, content=content_content[:100]
        ))

        yield final_history, formatted_reference, reasoning_content, content_content, final_first_token_time_str


class AllChatHandler(BaseChatHandler):
    """全库问答聊天处理器"""

    async def chat_stream(self, query: str, model_id: str, user_id: str, top_k: int,
                         top_r: int, min_score: float, mode: str, deep_thinking: bool,
                         history_display: str, collections: List[str] = None) -> AsyncGenerator[Tuple[str, str, str, str, str], None]:
        """全库问答 - 流式输出版本"""
        logger.info("ALL chat_stream被调用")

        if not self._validate_input(query):
            yield history_display, "", "", "", "0.0秒"
            return

        timing = self._create_timing_manager()
        timing.start_timing()

        payload = RequestHelper.create_payload(
            query=query,
            user_id=user_id,
            model_id=model_id,
            history=self.chat_app.get_conversation_history("allqa"),
            stream=True,
            top_k=top_k,
            top_r=top_r,
            min_score=min_score,
            mode=mode,
            enable_thinking=deep_thinking,
            collection=collections
        )

        logger.info(LogHelper.format_log_message(
            "all", "请求开始", payload["msg_id"], user_id, model_id, query, top_k=top_k
        ))

        reference_content = ""
        reasoning_content = ""
        content_content = ""

        try:
            async for chunk in self.api_client.stream_api_call("all-qa", payload):
                chunk_type = chunk.get("type", "")
                chunk_content = chunk.get("content", "")

                if chunk_type == "reference":
                    timing.start_reference_timing()
                    reference_content += chunk_content
                    formatted_reference = self.response_processor.format_reference_display(reference_content)
                    first_token_time_str = f"{timing.get_first_token_time():.2f}秒"
                    yield history_display, formatted_reference, reasoning_content, content_content, first_token_time_str
                elif chunk_type == "reasoning":
                    timing.start_reasoning_timing()
                    reasoning_content += chunk_content
                    formatted_reference = self.response_processor.format_reference_display(reference_content)
                    first_token_time_str = f"{timing.get_first_token_time():.2f}秒"
                    yield history_display, formatted_reference, reasoning_content, content_content, first_token_time_str
                elif chunk_type == "content":
                    timing.start_content_timing()
                    content_content += chunk_content
                    formatted_reference = self.response_processor.format_reference_display(reference_content)
                    first_token_time_str = f"{timing.get_first_token_time():.2f}秒"
                    yield history_display, formatted_reference, reasoning_content, content_content, first_token_time_str

        except Exception as e:
            error_msg = f"**API调用失败:** {str(e)}"
            logger.error(LogHelper.format_log_message(
                "all", "异常", payload["msg_id"], user_id, model_id, query, error=str(e)
            ))
            content_content = error_msg
            formatted_reference = self.response_processor.format_reference_display(reference_content)
            first_token_time_str = f"{timing.get_first_token_time():.2f}秒"
            yield history_display, formatted_reference, reasoning_content, content_content, first_token_time_str

        self.chat_app.add_to_history("allqa", query, content_content)

        new_history_entry = HistoryFormatter.format_history_entry(
            query=query,
            content=content_content,
            total_time=timing.get_total_time(),
            reference_time=timing.get_reference_time(),
            reasoning_time=timing.get_reasoning_time(),
            content_time=timing.get_content_time(),
            first_token_time=timing.get_first_token_time()
        )

        final_history = HistoryFormatter.append_to_history(history_display, new_history_entry)
        formatted_reference = self.response_processor.format_reference_display(reference_content)
        final_first_token_time_str = f"{timing.get_first_token_time():.2f}秒"

        logger.info(LogHelper.format_log_message(
            "all", "请求结束", payload["msg_id"], user_id, model_id, query, content=content_content[:100]
        ))

        yield final_history, formatted_reference, reasoning_content, content_content, final_first_token_time_str


class SearchHandler(BaseChatHandler):
    """搜索处理器"""

    async def search_stream(self, query: str, user_id: str, top_k: int, top_r: int = None, min_score: float = None, collections: list = None) -> AsyncGenerator[Tuple[str, str], None]:
        """检索功能 - 流式输出版本，返回(search_results, search_time)"""
        logger.info("search_stream被调用")

        if not self._validate_input(query):
            yield "", "0.0秒"
            return

        timing = self._create_timing_manager()
        timing.start_timing()

        payload = {
            "query": query,
            "user_id": user_id,
            "msg_id": RequestHelper.generate_request_id(),
            "top_k": top_k
        }

        # 添加检索参数
        if top_r is not None:
            payload["top_r"] = top_r
        if min_score is not None:
            payload["min_score"] = min_score
        
        # 添加collections参数
        if collections:
            payload["collections"] = collections
        print(f"检索参数 - top_k: {top_k}, top_r: {top_r}, min_score: {min_score}, collections: {collections}")
        logger.info(LogHelper.format_log_message(
            "search", "请求开始", payload["msg_id"], user_id, "", query, top_k=top_k
        ))

        search_results = ""
        first_result_received = False
        
        try:
            async for chunk in self.api_client.stream_api_call("search", payload):
                # 标记首个结果已接收
                if not first_result_received:
                    first_result_received = True
                    timing.mark_first_token()

                if isinstance(chunk, list):
                    # print(f"检索结果：{chunk}")
                    # 处理检索结果列表，格式化为更好的显示
                    formatted_data = ""
                    for item in chunk:
                        if isinstance(item, dict):
                            collection = item.get("collection", "未知集合")
                            refs = item.get("refs", [])
                            
                            # 集合名称映射
                            collection_names = {
                                "hardwareKnowledge": "硬工知识库",
                                "rDataQuery": "R平台数据",
                                "carKnowledge": "汽车知识库"
                            }
                            display_collection = collection_names.get(collection, collection)
                            
                            formatted_data += f"## 📚 {display_collection}\n\n"
                            
                            if refs:
                                for i, ref in enumerate(refs, 1):
                                    if isinstance(ref, dict):
                                        title = ref.get("title", "无标题")
                                        content = ref.get("content", "无内容")
                                        doc_name = ref.get("docName", "")
                                        doc_url = ref.get("docUrl", "")
                                        doc_type = ref.get("doc_type", "")
                                        update_time = ref.get("update_time", "")
                                        
                                        formatted_data += f"### {i}. {title if title else doc_name}\n"
                                        
                                        if content:
                                            # 清理内容中的【内容】标记
                                            formatted_data += f"**内容：**\n{content}\n\n"
                                        
                                        # 添加元数据
                                        metadata = []
                                        if doc_type:
                                            metadata.append(f"类型: {doc_type}")
                                        if update_time:
                                            metadata.append(f"更新时间: {update_time}")
                                        if doc_url:
                                            metadata.append(f"[查看原文]({doc_url})")
                                        
                                        if metadata:
                                            formatted_data += f"**文档信息：** {' | '.join(metadata)}\n\n"
                                        
                                        formatted_data += "---\n\n"
                                    else:
                                        formatted_data += f"{i}. {ref}\n\n"
                            else:
                                formatted_data += "暂无相关参考内容\n\n"
                    
                    search_results = formatted_data
                    # 显示检索时间，添加完成标记
                    search_time_str = f"{timing.get_first_token_time():.2f}秒"
                    if first_result_received:
                        search_time_str += "✓"
                    yield search_results, search_time_str
                   
                else:
                    # 如果chunk不是字典，直接作为内容处理
                    search_results += str(chunk)
                    search_time_str = f"{timing.get_first_token_time():.2f}秒"
                    if first_result_received:
                        search_time_str += "✓"
                    yield search_results, search_time_str

        except Exception as e:
            error_msg = f"**检索失败:** {str(e)}"
            logger.error(LogHelper.format_log_message(
                "search", "异常", payload["msg_id"], user_id, "", query, error=str(e)
            ))
            search_results = error_msg
            search_time_str = f"{timing.get_first_token_time():.2f}秒✓"
            yield search_results, search_time_str

        final_time = timing.get_total_time()
        logger.info(LogHelper.format_log_message(
            "search", "请求结束", payload["msg_id"], user_id, "", query,
            time=f"{final_time:.2f}秒"
        ))

        formatted_results = self.response_processor.format_reference_display(search_results)
        final_search_time_str = f"{timing.get_first_token_time():.2f}秒✓"
        yield formatted_results, final_search_time_str
