#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI组件模块
包含Gradio界面组件的创建和配置
"""

import gradio as gr
import asyncio
import os
import sys
from typing import Callable

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 导入配置
try:
    from frontend.frontend_config import DEFAULT_MODELS, DEFAULT_TOP_K, DEFAULT_MODEL, TITLE
    from loguru import logger
    logger.info("成功导入前端配置文件")
except ImportError:
    from loguru import logger
    logger.info("前端配置文件不存在，使用默认值")
    DEFAULT_MODELS = ["qwen3_32b", "gpt-4", "claude-3"]
    DEFAULT_TOP_K = 3
    DEFAULT_MODEL = "qwen3_32b"
    TITLE = "🤖 问题库AI"

from frontend.ui.styles import CUSTOM_CSS
from frontend.ui.scripts import CUSTOM_JAVASCRIPT


class UIComponents:
    """UI组件管理器"""
    
    def __init__(self):
        """初始化UI组件管理器"""
        self.components = {}
    
    def create_config_sidebar(self) -> dict:
        """创建配置侧边栏

        Returns:
            包含所有配置组件的字典
        """
        with gr.Column(elem_classes=["config-sidebar"], elem_id="config-sidebar-container"):
            # 折叠按钮和主标题
            with gr.Row(elem_classes=["sidebar-header"]):
                # collapse_btn = gr.Button(
                #     "◀",
                #     elem_classes=["collapse-btn"],
                #     elem_id="collapse-btn",
                #     scale=0,
                #     min_width=30
                # )
                gr.HTML('<h1 class="sidebar-title">🤖 问题库AI</h1>', elem_classes=["title-container"])

            # 首token响应时间 - 移到最上面，添加闪动控制，支持动态标签
            first_token_time = gr.Textbox(
                label="响应时间",
                value="0.0秒",
                interactive=False,
                elem_classes=["compact-input", "first-token-display"],
                elem_id="first-token-time-display"
            )

            # 动态配置区域 - 根据不同模式显示不同配置
            with gr.Column(elem_id="dynamic-config-area"):
                # LLM模式配置 - 默认显示
                llm_config_group = gr.Column(visible=False, elem_id="llm-config")
                with llm_config_group:
                    
                    # 对话配置
                    # gr.Markdown("对话配置", elem_classes=["config-section"])
                    
                    # 深度思考开关
                    llm_deep_thinking = gr.Checkbox(
                        label="开启深度思考",
                        value=True,
                        elem_classes=["compact-checkbox"]
                    )
                    
                    # 联网搜索开关
                    llm_web_search = gr.Checkbox(
                        label="开启联网搜索",
                        value=False,
                        elem_classes=["compact-checkbox"]
                    )
                    
                    # LLM问答模式下不显示问答模式选项
                    # llm_mode 组件被移除，因为LLM问答不需要模式选择
                    
                    user_id = gr.Textbox(
                        label="用户ID",
                        value="user_id",
                        show_label=True,
                        placeholder="请输入用户ID",
                        elem_classes=["compact-input"],
                        interactive=True
                    )
                    conversation_id = gr.Textbox(
                        label="对话ID", 
                        value="conv_id",
                        show_label=True,
                        placeholder="请输入对话ID",
                        elem_classes=["compact-input"],
                        interactive=True
                    )
                    model_id = gr.Dropdown(
                        label="问答模型",
                        choices=DEFAULT_MODELS,
                        value=DEFAULT_MODEL,
                        elem_classes=["compact-dropdown"],
                        interactive=True
                    )


                    llm_temperature = gr.Slider(
                        label="温度",
                        minimum=0.0,
                        maximum=2.0,
                        value=0.7,
                        step=0.1,
                        elem_classes=["compact-slider"]
                    )
                    llm_top_p = gr.Slider(
                        label="Top-p",
                        minimum=0.0,
                        maximum=1.0,
                        value=0.95,
                        step=0.05,
                        elem_classes=["compact-slider"]
                    )

                # RAG模式配置（硬工知识库、R平台问答、汽车知识库、全库问答） - 默认隐藏
                rag_config_group = gr.Column(visible=False, elem_id="rag-config")
                with rag_config_group:
                    
                    # 对话配置
                    # gr.Markdown("### 对话配置", elem_classes=["config-section"])
                    
                    # 深度思考开关（RAG模式）
                    rag_deep_thinking = gr.Checkbox(
                        label="开启深度思考",
                        value=True,
                        elem_classes=["compact-checkbox"]
                    )
                    
                    # 问答模式选择（RAG模式专用） - 改为选择框，默认选中严谨模式
                    rag_mode = gr.Radio(
                        label="问答模式",
                        choices=["通用", "严谨"],
                        value="严谨",
                        elem_classes=["compact-radio", "inline-radio"],
                        interactive=True
                    )

                    # 注释掉硬工知识库、汽车知识库、R平台、全库问答模式下的用户ID和对话ID配置
                    # rag_user_id = gr.Textbox(
                    #     label="用户ID",
                    #     value="用户ID",
                    #     placeholder="请输入用户ID",
                    #     show_label=False,
                    #     elem_classes=["compact-input"],
                    #     interactive=True
                    # )
                    # rag_conversation_id = gr.Textbox(
                    #     label="对话ID",
                    #     value="对话ID",
                    #     placeholder="请输入对话ID",
                    #     show_label=False,
                    #     elem_classes=["compact-input"],
                    #     interactive=True
                    # )
                    rag_model_id = gr.Dropdown(
                        label="问答模型",
                        show_label=True,
                        choices=DEFAULT_MODELS,
                        value=DEFAULT_MODEL,
                        elem_classes=["compact-dropdown"],
                        interactive=True
                    )
                    
                    # 集合选择（仅全库问答模式显示）
                    rag_collections = gr.CheckboxGroup(
                        label="检索集合",
                        choices=[
                            ("汽车知识库", "car"),
                            ("硬工知识库", "hardware"),
                            ("ISC知识库", "isc"),
                            ("R平台", "data")
                        ],
                        value=["car", "hardware", "data", "isc"],  # 默认选择所有集合
                        elem_classes=["compact-checkbox-group"],
                        interactive=True,
                        visible=False  # 默认隐藏，只在全库问答模式显示
                    )
                    
                    top_k = gr.Slider(
                        label="检索数量",
                        minimum=1,
                        maximum=60,
                        value=DEFAULT_TOP_K,
                        step=1,
                        elem_classes=["compact-slider"]
                    )
                    top_r = gr.Slider(
                        label="重排数量",
                        minimum=1,
                        maximum=20,
                        value=20,
                        step=1,
                        elem_classes=["compact-slider"],
                        interactive=True
                    )
                    min_score = gr.Slider(
                        label="最小相似度",
                        minimum=0.0,
                        maximum=1.0,
                        value=0.5,
                        step=0.1,
                        elem_classes=["compact-slider"],
                        interactive=True
                    )

                # 检索模式配置（只有检索功能） - 默认隐藏，美化居中显示
                search_config_group = gr.Column(visible=False, elem_id="search-config")
                with search_config_group:
                    gr.HTML('<div class="search-config-title">🔍 检索配置</div>', elem_classes=["search-config-header"])

                    # 集合选择
                    search_collections = gr.CheckboxGroup(
                        label="检索集合",
                        choices=[
                            ("汽车知识库", "car"),
                            ("硬工知识库", "hardware"),
                            ("R平台", "data"),
                            ("ISC知识库", "isc")
                        ],
                        value=["car", "hardware", "data", "isc"],  # 默认选择所有集合
                        elem_classes=["compact-checkbox-group", "search-config-checkbox"],
                        interactive=True
                    )

                    search_top_k = gr.Slider(
                        label="检索数量",
                        minimum=1,
                        maximum=60,
                        value=DEFAULT_TOP_K,
                        step=1,
                        elem_classes=["compact-slider"]
                    )
                    search_top_r = gr.Slider(
                        label="重排数量",
                        minimum=1,
                        maximum=20,
                        value=20,
                        step=1,
                        elem_classes=["compact-slider"],
                        interactive=True
                    )
                    search_min_score = gr.Slider(
                        label="最小相似度",
                        minimum=0.0,
                        maximum=1.0,
                        value=0.5,
                        step=0.1,
                        elem_classes=["compact-slider"],
                        interactive=True
                    )

            # 历史清理 - 底部只有一个按钮
            clear_current_btn = gr.Button(
                "清空当前模式历史",
                variant="secondary",
                elem_classes=["clear-history-btn"],
                elem_id="clear-current-history"
            )

        return {
            # "collapse_btn": collapse_btn,
            "user_id": user_id,
            "conversation_id": conversation_id,
            "first_token_time": first_token_time,
            "model_id": model_id,
            # LLM配置 - 移除了llm_mode
            "llm_deep_thinking": llm_deep_thinking,
            "llm_web_search": llm_web_search,
            "llm_temperature": llm_temperature,
            "llm_top_p": llm_top_p,
            "llm_config_group": llm_config_group,
            # RAG配置
            "rag_mode": rag_mode,
            "rag_deep_thinking": rag_deep_thinking,
            "rag_collections": rag_collections,
            # "rag_user_id": rag_user_id,
            # "rag_conversation_id": rag_conversation_id,
            "rag_model_id": rag_model_id,
            "top_k": top_k,
            "top_r": top_r,
            "min_score": min_score,
            "rag_config_group": rag_config_group,
            # 检索配置
            "search_collections": search_collections,
            "search_top_k": search_top_k,
            "search_top_r": search_top_r,
            "search_min_score": search_min_score,
            "search_config_group": search_config_group,
            # 其他
            "clear_current_btn": clear_current_btn
        }
    
    def create_chat_interface(self, tab_name: str, has_reference: bool = False) -> dict:
        """创建聊天界面

        Args:
            tab_name: 标签页名称
            has_reference: 是否包含参考内容

        Returns:
            包含聊天界面组件的字典
        """
        components = {}
        with gr.Row():
            query_input = gr.Textbox(
                placeholder="在这里输入您的问题...",
                lines=2,
                show_label=False,
                scale=5
            )
            send_btn = gr.Button(
                "发送",
                variant="primary",
                scale=1,
                elem_classes=["compact-button"]
            )

        components["query_input"] = query_input
        components["send_btn"] = send_btn

        # 响应显示区域 - 新布局：知识库参考在输入框下面左侧，思考过程和回复在右侧
        if has_reference:
            with gr.Row(elem_classes=["main-response-area"], equal_height=False):
                # 左侧：知识库参考
                with gr.Column(scale=1, elem_classes=["left-reference-column"], min_width=300):
                    with gr.Accordion("参考知识", open=True, elem_classes=["collapsible-reference"]):
                        reference_output = gr.Markdown(
                            value="",
                            elem_classes=["component-border", "dynamic-height", "reference-content"],
                            show_copy_button=True
                        )
                        components["reference_output"] = reference_output

                # 右侧：思考过程和回复内容
                with gr.Column(scale=1, elem_classes=["right-content-column"], min_width=300):
                    # 思考过程 - 支持折叠
                    with gr.Accordion("思考过程", open=True, elem_classes=["collapsible-reasoning"]):
                        reasoning_output = gr.Markdown(
                            value="",
                            elem_classes=["component-border", "fixed-height", "reasoning-content"],
                            show_copy_button=True
                        )
                        components["reasoning_output"] = reasoning_output

                    # 回复内容
                    with gr.Accordion("回复内容", open=True, elem_classes=["collapsible-content"]):
                        content_output = gr.Markdown(
                            value="",
                            elem_classes=["component-border", "dynamic-height"],
                            show_copy_button=True
                        )
                        components["content_output"] = content_output
        else:
            # 无参考内容的布局（LLM问答）
            # 思考过程 - 支持折叠
            with gr.Accordion("思考过程", open=True, elem_classes=["collapsible-reasoning"]):
                reasoning_output = gr.Markdown(
                    value="",
                    elem_classes=["component-border", "fixed-height", "reasoning-content"],
                    show_copy_button=True
                )
                components["reasoning_output"] = reasoning_output

            # 回复内容
            with gr.Accordion("回复内容", open=True, elem_classes=["collapsible-content"]):
                content_output = gr.Markdown(
                    value="",
                    elem_classes=["component-border", "dynamic-height"],
                    show_copy_button=True
                )
                components["content_output"] = content_output

        # 对话历史
        with gr.Row():
            with gr.Column():
                with gr.Accordion("对话历史", open=True, elem_classes=["collapsible-history"]):
                    history_output = gr.Markdown(
                        value="",
                        elem_classes=["component-border", "dynamic-height", "history-content"],
                        show_copy_button=True
                    )
                    components["history_output"] = history_output

        return components
    
    def create_search_interface(self) -> dict:
        """创建搜索界面

        Returns:
            包含搜索界面组件的字典
        """
        components = {}

        with gr.Column(elem_classes=["tab-content"]):
            # 用户输入区域
            with gr.Row():
                query_input = gr.Textbox(
                    placeholder="在这里输入检索关键词...",
                    lines=2,
                    show_label=False,
                    scale=5
                )
                search_btn = gr.Button(
                    "检索",
                    variant="primary",
                    scale=1,
                    elem_classes=["small-button"]
                )

            components["query_input"] = query_input
            components["search_btn"] = search_btn

            # 检索结果（数据参考）
            with gr.Row():
                with gr.Column():
                    with gr.Accordion("参考知识", open=True, elem_classes=["collapsible-data-reference"]):
                        search_output = gr.Markdown(
                            value="",
                            elem_classes=["component-border", "dynamic-height", "data-reference-content"],
                            show_copy_button=True
                        )
                        components["search_output"] = search_output

        return components
    
    def wrap_async_generator(self, async_gen_func: Callable) -> Callable:
        """包装异步生成器为同步生成器
        
        Args:
            async_gen_func: 异步生成器函数
            
        Returns:
            同步生成器函数
        """
        def sync_wrapper(*args, **kwargs):
            async def async_gen():
                async for result in async_gen_func(*args, **kwargs):
                    yield result
            
            # 运行异步生成器
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                gen = async_gen()
                while True:
                    try:
                        yield loop.run_until_complete(gen.__anext__())
                    except StopAsyncIteration:
                        break
            finally:
                loop.close()
        
        return sync_wrapper
    
    def get_full_css_and_js(self) -> str:
        """获取完整的CSS和JavaScript代码
        
        Returns:
            包含CSS和JavaScript的HTML字符串
        """
        return f"""
        <style>
        {CUSTOM_CSS}
        </style>
        {CUSTOM_JAVASCRIPT}
        """
