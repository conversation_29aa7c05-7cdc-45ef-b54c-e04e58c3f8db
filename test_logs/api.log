2025-08-20 19:45:41.002 | INFO | test-123 | __main__:<module\>:15 | LLM问答请求参数：query="使用淘宝 NPM 镜像加速二进制下载✔️ 针对 canvas 包的特殊优化配置     | ✅ 验证镜像地址有效性（访问 `https://npmmirror.com/mirrors/canvas/`）<\>" user_id="user_id" model_id="qwen3_32b"
2025-08-20 19:46:01.326 | INFO | test-123 | __main__:main:31 | LLM问答请求参数：query='使用淘宝 NPM 镜像加速二进制下载✔️ 针对 canvas 包的特殊优化配置     | ✅ 验证镜像地址有效性（访问 `https://npmmirror.com/mirrors/canvas/`）<\>' user_id='user_id' model_id='qwen3_32b'
2025-08-20 19:46:01.326 | INFO | test-1 | __main__:main:48 | 测试用例 1: 测试<think\>标签</think\>
2025-08-20 19:46:01.327 | INFO | test-2 | __main__:main:48 | 测试用例 2: 错误信息: <error\>Something went wrong</error\>
2025-08-20 19:46:01.327 | INFO | test-3 | __main__:main:48 | 测试用例 3: 空角括号: <\>
2025-08-20 19:46:01.327 | INFO | test-4 | __main__:main:48 | 测试用例 4: 单个角括号: \< 和 \>
2025-08-20 19:46:01.327 | INFO | test-5 | __main__:main:48 | 测试用例 5: 嵌套角括号: \<<nested\>\>
