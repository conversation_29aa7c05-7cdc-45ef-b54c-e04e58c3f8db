from typing import List, Dict, Any, AsyncGenerator, Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.llm_provider import get_llm_provider
from prompts.llm_qa_prompt import system_prompt, user_query, system_prompt_235B_2503
from services.websearch_service import get_websearch_service
from utils.token_limiter import CharacterLimiter
import json
from loguru import logger
from config.logging_config import configure_logging 
configure_logging()

class LLMQA:
    """LLM问答基础类，支持流式和非流式输出"""
    
    def __init__(self, model_id: str, request_id: str = None):
        """
        初始化问答实例

        Args:
            model_id: 模型ID (e.g. "gpt_4o", "qwen3_32b", "qwen3_235b_2507")
            request_id: 可选请求ID
        """
        self.model_id = model_id
        self.request_id = request_id
        # 实例化字符数限制器
        self.char_limiter = CharacterLimiter(max_chars=120000)
        self.logger = logger.bind(request_id=request_id)

    def _get_provider(self, enable_thinking: bool = True):
        """根据enable_thinking参数获取合适的provider"""
        return get_llm_provider(self.model_id, self.request_id, enable_thinking)
    
    def _build_messages(self, query: str, history: List[Dict], web_search_context: str = "") -> List[Dict]:
        """构建OpenAI格式的消息列表（适配新history格式）"""
        self.logger.info(f"构建消息列表: {query}")
        
        # 选择合适的系统prompt
        base_prompt = system_prompt_235B_2503 if self.model_id == "qwen3_235b_2507" else system_prompt
        if web_search_context:
            # 如果有联网搜索上下文，使用带搜索的系统prompt
            system_msg = base_prompt + "\n\n## 联网搜索信息使用说明：\n" + \
                        "当用户问题涉及最新信息、时事新闻、实时数据时，我会为你提供联网搜索的相关信息。" + \
                        "请结合这些搜索信息和你的知识来回答用户问题，并在适当时候注明信息来源于联网搜索。"
        else:
            system_msg = base_prompt
            
        messages = [{"role": "system", "content": system_msg}]
        
        # print(f"history: {history}")
        # 适配新格式history
        for item in history[::-1]:
            # 安全检查：确保item是字典类型且包含必要的键
            if isinstance(item, dict) and "query" in item and "content" in item:
                messages.append({"role": "user", "content": item["query"]})
                messages.append({"role": "assistant", "content": item["content"]})
            else:
                # 记录无效的历史记录项
                self.logger.warning(f"跳过无效的历史记录项: {item}")
        
        # 构建用户查询，如果有搜索上下文则添加
        if web_search_context:
            formatted_query = f"{web_search_context}\n\n用户问题：{query}"
        else:
            formatted_query = user_query.format(query=query)
            
        messages.append({"role": "user", "content": formatted_query})
        return messages
    
    async def generate(
        self,
        query: str,
        user_id: str,
        history: List[Dict],
        timeout: Optional[float] = None,
        conversation_id: Optional[str] = None,
        enable_thinking: bool = False,
        enable_web_search: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """
        非流式问答生成
        """
        # 如果启用联网搜索，先进行搜索
        web_search_context = ""
        if enable_web_search:
            try:
                websearch_service = get_websearch_service()
                search_result = await websearch_service.search(query, user_id)
                if 'webPages' in search_result:
                    summaries = websearch_service.extract_summaries(search_result)
                    web_search_context = websearch_service.format_search_context(summaries)
                    self.logger.info(f"联网搜索成功，获取上下文长度: {len(web_search_context)}")
                else:
                    self.logger.warning(f"联网搜索失败: {search_result.get('error', '未知错误')}")
            except Exception as e:
                self.logger.error(f"联网搜索异常: {str(e)}")

        # 应用字符数长度限制
        limited_query, limited_history, limited_web_context = self.char_limiter.limit_messages_for_llm_qa(
            query, history, web_search_context
        )

        messages = self._build_messages(limited_query, limited_history, limited_web_context)
        provider = self._get_provider(enable_thinking)
        return await provider.generate(
            messages=messages,
            timeout=timeout,
            conversation_id=conversation_id,
            enable_thinking=enable_thinking,
            **kwargs
        )
    
    async def generate_stream(
        self,
        query: str,
        user_id: str,
        history: List[Dict],
        timeout: Optional[float] = None,
        conversation_id: Optional[str] = None,
        enable_thinking: bool = True,
        enable_web_search: bool = False,
        **kwargs
    ) -> AsyncGenerator[Dict[str, Any], None]:
        # 如果启用联网搜索，先进行搜索
        web_search_context = ""
        if enable_web_search:
            try:
                websearch_service = get_websearch_service()
                search_result = await websearch_service.search(query, user_id)
                if 'webPages' in search_result:
                    summaries = websearch_service.extract_summaries(search_result)
                    web_search_context = websearch_service.format_search_context(summaries)
                    self.logger.info(f"联网搜索成功，获取上下文长度: {len(web_search_context)}")
                else:
                    self.logger.warning(f"联网搜索失败: {search_result.get('error', '未知错误')}")
            except Exception as e:
                self.logger.error(f"联网搜索异常: {str(e)}")

        # 应用字符数长度限制
        limited_query, limited_history, limited_web_context = self.char_limiter.limit_messages_for_llm_qa(
            query, history, web_search_context
        )

        # 模型服务的流式调用
        messages = self._build_messages(limited_query, limited_history, limited_web_context)
        self.logger.info(f"对话消息列表: {json.dumps(messages,ensure_ascii=False)[:500]}...")
        # print(f"message: {messages}")
        provider = self._get_provider(enable_thinking)
        async for chunk in provider.generate_stream(
            messages=messages,
            timeout=timeout,
            conversation_id=conversation_id,
            enable_thinking=enable_thinking,
            **kwargs
        ):
            yield chunk
                
