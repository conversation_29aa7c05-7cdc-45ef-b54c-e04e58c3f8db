from typing import List, Dict, Any, AsyncGenerator, Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.llm_provider import get_llm_provider
from prompts.data_qa_prompt import data_qa_sys_prompt, data_qa_user_prompt, data_qa_rerank_prompt
from services.search_service import SearchService
from services.rerank_service import RerankService

from loguru import logger
from config.logging_config import configure_logging
from config.all_search_config import ALL_SEARCH_COLLECTIONS, ALL_SEARCH_MODEL_CONFIG, ALL_SEARCH_RERANK_CONFIG, DATA_SEARCH_COLLECTIONS, HARDWARE_SEARCH_COLLECTIONS, CAR_SEARCH_COLLECTIONS, get_collections_by_types
from config.isc_search_config import ISC_SEARCH_COLLECTIONS
configure_logging()

import json
import asyncio

class SEARCH:
    """RAG问答类，支持流式和非流式输出"""
    
    def __init__(self, request_id: str = None):
        """
        初始化RAG问答实例
        
        Args:
            request_id: 可选请求ID
        """
        self.request_id = request_id
        self.search_service = SearchService(config = ALL_SEARCH_MODEL_CONFIG, request_id=request_id)
        self.rerank_service = RerankService(config = ALL_SEARCH_RERANK_CONFIG, request_id=request_id)
        self.logger = logger.bind(request_id=request_id)
    
    async def _retrieve_knowledge(self, query: str, user_id: str, collection_name: str, top_k: int = 60, min_score: float = None):
        """检索单个库并重排，不做top_r过滤"""
        self.logger.info(f"检索库: {collection_name}, 用户ID: {user_id}, 查询: {query}, top_k: {top_k}, min_score: {min_score}")
        search_results, error = await self.search_service.search(
            user_id=user_id,
            query=query,
            top_k=top_k,
            collection_name=collection_name
        )
        if error or not search_results:
            return []
        self.logger.info(f"库 {collection_name} 检索到知识: {len(search_results)} 条")
        # print(f"检索到的知识: {search_results}")
        # 重排知识 - 使用DATAQA专门的rerank prompt
        reranked_docs = await self.rerank_service.rerank_with_prompt(
            query=query,
            documents=search_results,
            instruction=data_qa_rerank_prompt,
            top_r=top_k,
            min_score=min_score
        )
        self.logger.info(f"库 {collection_name} 重排后知识: {len(reranked_docs)} 条")
        return reranked_docs

    async def _retrieve_and_rerank_all_collections(self, query: str, user_id: str, collections, top_k: int = 20, top_r: int = None, min_score: float = None):
        """异步检索指定collections并重排，按分组返回每组前20条"""
        tasks = []
        collection_names = []
        for collection in collections:
            collection_name = collection.get("collection_name")
            if collection_name:
                tasks.append(self._retrieve_knowledge(query, user_id, collection_name, top_k=top_k, min_score=min_score))
                collection_names.append(collection_name)
        all_results = await asyncio.gather(*tasks)

        # 合并结果，按集合类型分组
        data_collections = set([c["collection_name"] for c in DATA_SEARCH_COLLECTIONS])
        hardware_collections = set([c["collection_name"] for c in HARDWARE_SEARCH_COLLECTIONS])
        car_collections = set([c["collection_name"] for c in CAR_SEARCH_COLLECTIONS])
        isc_collections = set([c["collection_name"] for c in ISC_SEARCH_COLLECTIONS])

        data_group = []
        hardware_group = []
        car_group = []
        isc_group = []

        for collection_name, docs in zip(collection_names, all_results):
            if collection_name in data_collections:
                data_group.extend(docs)
            elif collection_name in hardware_collections:
                hardware_group.extend(docs)
            elif collection_name in car_collections:
                car_group.extend(docs)
            elif collection_name in isc_collections:
                isc_group.extend(docs)

        # 按score排序，每组取前20
        data_group.sort(key=lambda x: x.get("score", 0), reverse=True)
        hardware_group.sort(key=lambda x: x.get("score", 0), reverse=True)
        car_group.sort(key=lambda x: x.get("score", 0), reverse=True)
        isc_group.sort(key=lambda x: x.get("score", 0), reverse=True)

        # 构建结果，只包含有数据的集合
        res = []
        if hardware_group:
            res.append({
                "collection": "hardwareKnowledge",
                "refs": hardware_group[:20]
            })
        if data_group:
            res.append({
                "collection": "rDataQuery",
                "refs": data_group[:20]
            })
        if car_group:
            res.append({
                "collection": "carKnowledge",
                "refs": car_group[:20]
            })
        if isc_group:
            res.append({
                "collection": "iscKnowledge",
                "refs": isc_group[:20]
            })

        return res

    def format_knowledge(self, reranked_docs):
        """格式化重排后的知识为字符串"""
        formatted_docs = []
        for i, doc in enumerate(reranked_docs):
            formatted_doc = f"\n检索结果{i+1}:\n"
            formatted_doc += f"{doc.get('content', '')}\n"
            formatted_docs.append(formatted_doc)
        self.logger.info(f"格式化后的知识, 共: {len(formatted_docs)}条")
        return "\n\n".join(formatted_docs), reranked_docs
    
    def deduplicate_by_docurl(self,docs):
        seen = set()
        deduped = []
        for doc in docs:
            url = doc.get("docUrl")
            if url not in seen:
                deduped.append(doc)
                seen.add(url)
        return deduped
    
    async def search_all_collections(
        self,
        query: str,
        user_id: str,
        timeout: Optional[float] = None,
        top_k: int = 60,
        top_r: int = None,
        min_score: float = None,
        collections: Optional[List[str]] = None,
        **kwargs
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        检索内容

        Args:
            query: 查询字符串
            user_id: 用户ID
            timeout: 超时时间
            top_k: 检索数量
            top_r: 重排数量
            min_score: 最小相似度
            collections: 要检索的集合类型列表，如 ["car", "hardware"] 或 ["data"]
                        如果为None或空列表，则检索所有集合
        """
        # 根据collections参数获取要检索的集合
        if collections:
            target_collections = get_collections_by_types(collections)
            self.logger.info(f"指定检索集合类型: {collections}, 对应集合数量: {len(target_collections)}")
        else:
            target_collections = ALL_SEARCH_COLLECTIONS
            self.logger.info(f"检索所有集合，集合数量: {len(target_collections)}")

        # 检索指定库并重排，分组
        grouped_res = await self._retrieve_and_rerank_all_collections(query, user_id, target_collections, top_k=top_k, top_r=top_r, min_score=min_score)
        self.logger.info(f"检索完成")
        print(f"检索到的知识：{grouped_res}")
        yield grouped_res