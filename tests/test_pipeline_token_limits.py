#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Pipeline Token限制集成测试
验证各个pipeline是否正确应用了token限制
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 添加环境变量加载代码
from dotenv import load_dotenv

# 获取环境变量中的环境类型，默认为local
env_type = os.environ.get("ENVIRONMENT", "local")
env_file = f".env.{env_type}"

# 尝试加载对应环境的配置文件
env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), env_file)
if os.path.exists(env_path):
    load_dotenv(env_path)
    print(f"已加载{env_type}环境配置: {env_path}")
else:
    # 如果特定环境配置不存在，尝试加载默认.env文件
    default_env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
    if os.path.exists(default_env_path):
        load_dotenv(default_env_path)
        print(f"已加载默认环境配置: {default_env_path}")
    else:
        print(f"警告: 环境配置文件不存在: {env_path} 或 {default_env_path}")

import pytest
from unittest.mock import Mock, patch, AsyncMock
from pipelines.all_qa import ALLQA
from pipelines.car_qa import CARQA
from pipelines.isc_qa import ISCQA
from pipelines.rag_qa import RAGQA
from pipelines.data_qa import DATAQA
from pipelines.llm_qa import LLMQA
from pipelines.doc_qa import DocQA


class TestPipelineTokenLimits:
    """Pipeline Token限制集成测试类"""
    
    def test_all_qa_has_token_limiter(self):
        """测试ALLQA是否正确初始化了token限制器"""
        qa = ALLQA(model_id="test_model", request_id="test_request")
        assert hasattr(qa, 'token_limiter')
        assert qa.token_limiter.max_tokens == 120000
    
    def test_car_qa_has_token_limiter(self):
        """测试CARQA是否正确初始化了token限制器"""
        qa = CARQA(model_id="test_model", request_id="test_request")
        assert hasattr(qa, 'token_limiter')
        assert qa.token_limiter.max_tokens == 120000
    
    def test_isc_qa_has_token_limiter(self):
        """测试ISCQA是否正确初始化了token限制器"""
        qa = ISCQA(model_id="test_model", request_id="test_request")
        assert hasattr(qa, 'token_limiter')
        assert qa.token_limiter.max_tokens == 120000
    
    def test_rag_qa_has_token_limiter(self):
        """测试RAGQA是否正确初始化了token限制器"""
        qa = RAGQA(model_id="test_model", request_id="test_request")
        assert hasattr(qa, 'token_limiter')
        assert qa.token_limiter.max_tokens == 120000
    
    def test_data_qa_has_token_limiter(self):
        """测试DATAQA是否正确初始化了token限制器"""
        qa = DATAQA(model_id="test_model", request_id="test_request")
        assert hasattr(qa, 'token_limiter')
        assert qa.token_limiter.max_tokens == 120000
    
    def test_llm_qa_has_token_limiter(self):
        """测试LLMQA是否正确初始化了token限制器"""
        qa = LLMQA(model_id="test_model", request_id="test_request")
        assert hasattr(qa, 'token_limiter')
        assert qa.token_limiter.max_tokens == 120000
    
    def test_doc_qa_has_token_limiter(self):
        """测试DocQA是否正确初始化了token限制器"""
        qa = DocQA(model_id="test_model", request_id="test_request")
        assert hasattr(qa, 'token_limiter')
        assert qa.token_limiter.max_tokens == 120000
    
    @patch('pipelines.all_qa.get_llm_provider')
    @patch.object(ALLQA, '_retrieve_and_rerank_all_collections')
    async def test_all_qa_applies_token_limits(self, mock_retrieve, mock_provider):
        """测试ALLQA是否正确应用token限制"""
        # 设置mock
        mock_retrieve.return_value = []
        mock_provider_instance = Mock()
        mock_provider_instance.generate_stream = AsyncMock()
        mock_provider_instance.generate_stream.return_value = iter([
            {"type": "content", "content": "test response", "role": "assistant", "finish_reason": ""}
        ])
        mock_provider.return_value = mock_provider_instance
        
        # 创建测试数据
        qa = ALLQA(model_id="test_model", request_id="test_request")
        
        # 创建超长的测试数据
        long_query = "这是一个很长的查询" * 1000
        long_history = [
            {"query": "历史查询" * 500, "content": "历史回答" * 500}
            for _ in range(10)
        ]
        
        # Mock token限制器的方法来验证是否被调用
        with patch.object(qa.token_limiter, 'limit_messages_for_rag_qa') as mock_limit:
            mock_limit.return_value = ("limited_query", [], "limited_knowledge")
            
            # 调用generate_stream方法
            result_generator = qa.generate_stream(
                query=long_query,
                user_id="test_user",
                history=long_history
            )
            
            # 消费生成器
            results = []
            async for chunk in result_generator:
                results.append(chunk)
                break  # 只取第一个chunk来验证
            
            # 验证token限制方法被调用
            mock_limit.assert_called_once()
            call_args = mock_limit.call_args[0]
            assert call_args[0] == long_query  # query
            assert call_args[1] == long_history  # history
    
    @patch('pipelines.llm_qa.get_llm_provider')
    async def test_llm_qa_applies_token_limits(self, mock_provider):
        """测试LLMQA是否正确应用token限制"""
        # 设置mock
        mock_provider_instance = Mock()
        mock_provider_instance.generate_stream = AsyncMock()
        mock_provider_instance.generate_stream.return_value = iter([
            {"type": "content", "content": "test response", "role": "assistant", "finish_reason": ""}
        ])
        mock_provider.return_value = mock_provider_instance
        
        # 创建测试数据
        qa = LLMQA(model_id="test_model", request_id="test_request")
        
        # 创建超长的测试数据
        long_query = "这是一个很长的查询" * 1000
        long_history = [
            {"query": "历史查询" * 500, "content": "历史回答" * 500}
            for _ in range(10)
        ]
        
        # Mock token限制器的方法来验证是否被调用
        with patch.object(qa.token_limiter, 'limit_messages_for_llm_qa') as mock_limit:
            mock_limit.return_value = ("limited_query", [], "limited_web_context")
            
            # 调用generate_stream方法
            result_generator = qa.generate_stream(
                query=long_query,
                user_id="test_user",
                history=long_history,
                enable_web_search=False
            )
            
            # 消费生成器
            results = []
            async for chunk in result_generator:
                results.append(chunk)
                break  # 只取第一个chunk来验证
            
            # 验证token限制方法被调用
            mock_limit.assert_called_once()
            call_args = mock_limit.call_args[0]
            assert call_args[0] == long_query  # query
            assert call_args[1] == long_history  # history
            assert call_args[2] == ""  # web_context (no web search)
    
    @patch('pipelines.doc_qa.get_llm_provider')
    @patch.object(DocQA, '_retrieve_knowledge')
    async def test_doc_qa_applies_token_limits(self, mock_retrieve, mock_provider):
        """测试DocQA是否正确应用token限制"""
        # 设置mock
        mock_retrieve.return_value = "这是文档内容" * 100
        mock_provider_instance = Mock()
        mock_provider_instance.generate_stream = AsyncMock()
        mock_provider_instance.generate_stream.return_value = iter([
            {"type": "content", "content": "test response", "role": "assistant", "finish_reason": ""}
        ])
        mock_provider.return_value = mock_provider_instance
        
        # 创建测试数据
        qa = DocQA(model_id="test_model", request_id="test_request")
        
        # 创建超长的测试数据
        long_query = "这是一个很长的查询" * 1000
        long_history = [
            {"query": "历史查询" * 500, "content": "历史回答" * 500}
            for _ in range(10)
        ]
        
        # Mock token限制器的方法来验证是否被调用
        with patch.object(qa.token_limiter, 'limit_messages_for_doc_qa') as mock_limit:
            mock_limit.return_value = ("limited_query", [], "limited_document")
            
            # 调用generate_stream方法
            result_generator = qa.generate_stream(
                query=long_query,
                user_id="test_user",
                conversation_id="test_conversation",
                history=long_history
            )
            
            # 消费生成器
            results = []
            async for chunk in result_generator:
                results.append(chunk)
                break  # 只取第一个chunk来验证
            
            # 验证token限制方法被调用
            mock_limit.assert_called_once()
            call_args = mock_limit.call_args[0]
            assert call_args[0] == long_query  # query
            assert call_args[1] == long_history  # history
    
    def test_token_limiter_configuration(self):
        """测试token限制器配置是否正确"""
        # 测试所有pipeline的token限制器都配置为120000
        pipelines = [
            ALLQA("test", "test"),
            CARQA("test", "test"),
            ISCQA("test", "test"),
            RAGQA("test", "test"),
            DATAQA("test", "test"),
            LLMQA("test", "test"),
            DocQA("test", "test")
        ]
        
        for pipeline in pipelines:
            assert hasattr(pipeline, 'token_limiter')
            assert pipeline.token_limiter.max_tokens == 120000
            
            # 验证token限制器的基本功能
            test_text = "测试文本" * 100
            limited = pipeline.token_limiter.truncate_text(test_text, 50)
            assert pipeline.token_limiter.estimate_tokens(limited) <= 50


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
