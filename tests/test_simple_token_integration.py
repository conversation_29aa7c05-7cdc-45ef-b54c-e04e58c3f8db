#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的字符数限制集成测试
验证CharacterLimiter类是否能正确导入和使用
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_character_limiter_import():
    """测试CharacterLimiter是否能正确导入"""
    try:
        from utils.token_limiter import CharacterLimiter
        print("✓ CharacterLimiter导入成功")
        return True
    except ImportError as e:
        print(f"✗ CharacterLimiter导入失败: {e}")
        return False

def test_character_limiter_basic_functionality():
    """测试CharacterLimiter基本功能"""
    try:
        from utils.token_limiter import CharacterLimiter

        # 创建实例
        limiter = CharacterLimiter(max_chars=120000)
        print(f"✓ CharacterLimiter实例创建成功，最大字符数: {limiter.max_chars}")

        # 测试字符数计算
        test_text = "这是一个测试文本"
        chars = limiter.count_chars(test_text)
        print(f"✓ 字符数计算功能正常，文本'{test_text}'字符数: {chars}")

        # 测试文本截断
        long_text = "这是一个很长的文本" * 100
        truncated = limiter.truncate_text(long_text, 50)
        truncated_chars = len(truncated)
        print(f"✓ 文本截断功能正常，原长度: {len(long_text)}, 截断后长度: {len(truncated)}, 字符数: {truncated_chars}")

        # 测试RAG问答限制 - 短内容不限制
        query = "测试查询"
        history = [{"query": "历史查询", "content": "历史回答"}]
        knowledge = "测试知识内容"

        limited_query, limited_history, limited_knowledge = limiter.limit_messages_for_rag_qa(
            query, history, knowledge
        )
        print(f"✓ RAG问答限制功能正常")

        # 测试LLM问答限制
        limited_query2, limited_history2, limited_web = limiter.limit_messages_for_llm_qa(
            query, history, "网络搜索内容"
        )
        print(f"✓ LLM问答限制功能正常")

        # 测试文档问答限制
        limited_query3, limited_history3, limited_doc = limiter.limit_messages_for_doc_qa(
            query, history, "文档内容"
        )
        print(f"✓ 文档问答限制功能正常")

        return True

    except Exception as e:
        print(f"✗ CharacterLimiter功能测试失败: {e}")
        return False

def test_pipeline_imports():
    """测试pipeline文件是否能正确导入CharacterLimiter"""
    pipeline_files = [
        "pipelines.all_qa",
        "pipelines.car_qa",
        "pipelines.isc_qa",
        "pipelines.rag_qa",
        "pipelines.data_qa",
        "pipelines.llm_qa",
        "pipelines.doc_qa"
    ]

    success_count = 0
    for pipeline_file in pipeline_files:
        try:
            # 检查文件是否包含CharacterLimiter导入
            file_path = pipeline_file.replace(".", "/") + ".py"
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if "from utils.token_limiter import CharacterLimiter" in content:
                    print(f"✓ {pipeline_file} 包含CharacterLimiter导入")
                    success_count += 1
                else:
                    print(f"✗ {pipeline_file} 缺少CharacterLimiter导入")
        except Exception as e:
            print(f"✗ 检查 {pipeline_file} 失败: {e}")

    print(f"总计: {success_count}/{len(pipeline_files)} 个pipeline文件包含CharacterLimiter导入")
    return success_count == len(pipeline_files)

def test_pipeline_character_limiter_usage():
    """测试pipeline文件是否正确使用CharacterLimiter"""
    pipeline_files = [
        "pipelines/all_qa.py",
        "pipelines/car_qa.py",
        "pipelines/isc_qa.py",
        "pipelines/rag_qa.py",
        "pipelines/data_qa.py",
        "pipelines/llm_qa.py",
        "pipelines/doc_qa.py"
    ]

    success_count = 0
    for pipeline_file in pipeline_files:
        try:
            with open(pipeline_file, 'r', encoding='utf-8') as f:
                content = f.read()

                # 检查是否初始化了char_limiter
                if "self.char_limiter = CharacterLimiter" in content:
                    print(f"✓ {pipeline_file} 正确初始化了char_limiter")

                    # 检查是否使用了限制方法
                    if ("limit_messages_for_rag_qa" in content or
                        "limit_messages_for_llm_qa" in content or
                        "limit_messages_for_doc_qa" in content):
                        print(f"✓ {pipeline_file} 正确使用了字符数限制方法")
                        success_count += 1
                    else:
                        print(f"✗ {pipeline_file} 未使用字符数限制方法")
                else:
                    print(f"✗ {pipeline_file} 未初始化char_limiter")
        except Exception as e:
            print(f"✗ 检查 {pipeline_file} 失败: {e}")

    print(f"总计: {success_count}/{len(pipeline_files)} 个pipeline文件正确使用CharacterLimiter")
    return success_count == len(pipeline_files)

def main():
    """主测试函数"""
    print("开始字符数限制功能集成测试...")
    print("=" * 50)

    tests = [
        ("CharacterLimiter导入测试", test_character_limiter_import),
        ("CharacterLimiter基本功能测试", test_character_limiter_basic_functionality),
        ("Pipeline导入检查", test_pipeline_imports),
        ("Pipeline使用检查", test_pipeline_character_limiter_usage)
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        if test_func():
            passed += 1
            print(f"✓ {test_name} 通过")
        else:
            print(f"✗ {test_name} 失败")

    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 个测试通过")

    if passed == total:
        print("🎉 所有测试通过！字符数限制功能已成功集成到所有pipeline中。")
        return True
    else:
        print("❌ 部分测试失败，请检查相关问题。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
