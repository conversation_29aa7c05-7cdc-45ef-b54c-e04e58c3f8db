"""API路由定义"""

from fastapi import APIRouter, HTTPException, Depends, Header
from fastapi.responses import StreamingResponse
import asyncio
import sys
import os
import time
from loguru import logger
import json
from pydantic import BaseModel
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.schemas import (
    LLMQARequest, LLMQAResponse,
    RAGQARequest, RAGQAResponse,
    DATAQARequest, DATAQAResponse,
    ISCQARequest, ISCQAResponse,
    ALLQARequest, ALLQAResponse,
    SearchRequest, SearchResponse,
    DocQARequest, DocQAResponse,
    ErrorResponse
)
from pipelines.llm_qa import LLMQA
from pipelines.rag_qa import RAGQA
from pipelines.car_qa import CARQA
from pipelines.data_qa import DATAQA
from pipelines.isc_qa import ISCQA
from pipelines.doc_qa import DocQA
from pipelines.all_qa import ALLQA
from pipelines.search import <PERSON>ARCH
from config.logging_config import configure_logging
from config.model_config import get_api_access_token
from config.hardware_search_config import HARDWARE_SEARCH_MODEL_CONFIG, HARDWARE_RERANK_MODEL_CONFIG
from config.data_search_config import DATA_SEARCH_MODEL_CONFIG
from config.data_search_config import DATA_RERANK_MODEL_CONFIG
from config.isc_search_config import ISC_SEARCH_MODEL_CONFIG, ISC_RERANK_MODEL_CONFIG
from config.all_qa_config import ALL_QA_SEARCH_CONFIG, ALL_QA_RERANK_CONFIG
from config.all_search_config import ALL_SEARCH_MODEL_CONFIG, ALL_SEARCH_RERANK_CONFIG
from config.car_search_config import CAR_SEARCH_MODEL_CONFIG, CAR_RERANK_MODEL_CONFIG


from datetime import datetime

# 配置日志
configure_logging()

# 创建路由
router = APIRouter(tags=["问答服务"])

# 错误代码映射
ERROR_CODES = {
    "INVALID_REQUEST": "请求参数无效",
    "MODEL_ERROR": "模型调用错误",
    "SEARCH_ERROR": "搜索服务错误",
    "INTERNAL_ERROR": "内部服务错误",
    "TIMEOUT_ERROR": "请求处理超时"
}

API_ACCESS_TOKEN = get_api_access_token()
print(f"API_ACCESS_TOKEN: {API_ACCESS_TOKEN}")

async def verify_api_token(
    Authorization: str = Header(None)):
    
    if not Authorization:
        logger.warning("API请求缺少令牌")
        raise HTTPException(status_code=401, detail="缺少访问令牌")
    # print(f"Authorization: {Authorization}")
    # print(f"API_ACCESS_TOKEN: {API_ACCESS_TOKEN}")
    if Authorization != API_ACCESS_TOKEN:
        logger.warning("API令牌验证失败: {}...", Authorization)
        # logger.error(f"正确的令牌为: {API_ACCESS_TOKEN}")
        raise HTTPException(status_code=403, detail="访问令牌无效")
    
    return Authorization

# 请求ID中间件

# LLM问答API
@router.post(
    "/llm-qa", 
    response_model=LLMQAResponse,
    responses={
        200: {"model": LLMQAResponse, "description": "成功响应"},
        400: {"model": ErrorResponse, "description": "请求参数错误"},
        500: {"model": ErrorResponse, "description": "服务器内部错误"}
    },
    dependencies=[Depends(verify_api_token)]
)
async def llm_qa(request: LLMQARequest):
    """LLM问答接口"""
    start_time = time.time()
    logger.info("LLM问答请求参数：{}", str(request), request_id=request.msg_id)
    try:
        # 校验必填参数
        if not request.msg_id or not request.conversation_id or request.history is None:
            raise HTTPException(
                status_code=400,
                detail=ErrorResponse(
                    success=False,
                    message="msg_id、conversation_id、history为必填参数",
                    error_code="INVALID_REQUEST",
                    error_detail={}
                ).model_dump()
            )
        msg_id = request.msg_id
        conversation_id = request.conversation_id
        logger.info("LLM问答请求开始: user_id={} | conv_id={} | query={}", request.user_id, request.conversation_id, request.query, request_id=msg_id)
        qa = LLMQA(model_id=request.model_id, request_id=msg_id)
        history = request.history
        logger.info(f"历史记录条数: {len(history)}", request_id=msg_id)
        if request.stream:
            async def generate_stream():
                stream_start_time = time.time()
                logger.info("开始生成流式响应", request_id=msg_id)
                try:
                    async for chunk in qa.generate_stream(
                        query=request.query,
                        user_id=request.user_id,
                        history=history,
                        timeout=600,
                        conversation_id=conversation_id,
                        enable_thinking=request.enable_thinking,
                        enable_web_search=request.enable_web_search
                    ):
                        if isinstance(chunk, dict):
                            chunk["msg_id"] = msg_id
                            chunk["conversation_id"] = conversation_id
                        yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
                    stream_end_time = time.time()
                    logger.info(f"LLM流式问答完成，耗时: {stream_end_time - stream_start_time:.2f}秒", request_id=msg_id)
                except Exception as e:
                    stream_end_time = time.time()
                    logger.error(f"流式生成错误: {str(e)}，耗时: {stream_end_time - stream_start_time:.2f}秒", request_id=msg_id)
                    error_json = ErrorResponse(
                        success=False,
                        message=ERROR_CODES["MODEL_ERROR"],
                        error_code="MODEL_ERROR",
                        error_detail={"error": str(e)}
                    ).model_dump_json()
                    yield f"data: {error_json}\n\n"
            return StreamingResponse(
                generate_stream(),
                media_type="text/event-stream"
            )
        logger.info(f"开始生成LLM回答", request_id=msg_id)
        result = await qa.generate(
            query=request.query,
            user_id=request.user_id,
            history=history,
            timeout=600,
            conversation_id=conversation_id,
            enable_thinking=request.enable_thinking,
            enable_web_search=request.enable_web_search
        )
        end_time = time.time()
        logger.info(f"LLM问答完成，耗时: {end_time - start_time:.2f}秒", request_id=msg_id)
        return LLMQAResponse(
            answer=result["content"],
            model_id=request.model_id,
            msg_id=msg_id,
            conversation_id=conversation_id
        )
    except asyncio.TimeoutError as e:
        end_time = time.time()
        logger.error(f"LLM问答超时: {str(e)}，耗时: {end_time - start_time:.2f}秒", request_id=request.msg_id)
        raise HTTPException(
            status_code=504,
            detail=ErrorResponse(
                success=False,
                message="请求处理超时",
                error_code="TIMEOUT_ERROR",
                error_detail={"error": str(e)}
            ).model_dump()
        )
    except Exception as e:
        end_time = time.time()
        logger.error(f"LLM问答错误: {str(e)}，耗时: {end_time - start_time:.2f}秒", request_id=request.msg_id)
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                success=False,
                message=ERROR_CODES["INTERNAL_ERROR"],
                error_code="INTERNAL_ERROR",
                error_detail={"error": str(e)}
            ).model_dump()
        )

# RAG问答API
@router.post(
    "/rag-qa", 
    response_model=RAGQAResponse,
    responses={
        200: {"model": RAGQAResponse, "description": "成功响应"},
        400: {"model": ErrorResponse, "description": "请求参数错误"},
        500: {"model": ErrorResponse, "description": "服务器内部错误"},
        504: {"model": ErrorResponse, "description": "请求处理超时"}
    },
    dependencies=[Depends(verify_api_token)]
)
async def rag_qa(request: RAGQARequest):
    """RAG问答接口"""
    start_time = time.time()
    logger.info("RAG问答请求参数：{}", str(request), request_id=request.msg_id)
    try:
        if not request.msg_id or not request.conversation_id or request.history is None:
            raise HTTPException(
                status_code=400,
                detail=ErrorResponse(
                    success=False,
                    message="msg_id、conversation_id、history为必填参数",
                    error_code="INVALID_REQUEST",
                    error_detail={}
                ).model_dump()
            )
        msg_id = request.msg_id
        conversation_id = request.conversation_id
        logger.info("RAG问答请求开始: user_id={} | conv_id={} | query={}", request.user_id, request.conversation_id, request.query, request_id=msg_id)
        # 新增 knowledge 参数分支
        knowledge_type = getattr(request, 'knowledge', None)
        if knowledge_type == "car":
            qa = CARQA(model_id=request.model_id, request_id=msg_id)
            top_k = request.top_k if request.top_k is not None else CAR_SEARCH_MODEL_CONFIG["default_params"]["top_k"]
            top_r = request.top_r if request.top_r is not None else CAR_RERANK_MODEL_CONFIG["default_params"]["top_r"]
            min_score = request.min_score if request.min_score is not None else CAR_RERANK_MODEL_CONFIG["default_params"]["min_score"]
        else:  # 默认 hardware
            qa = RAGQA(model_id=request.model_id, request_id=msg_id)
            top_k = request.top_k if request.top_k is not None else HARDWARE_SEARCH_MODEL_CONFIG["default_params"]["top_k"]
            top_r = request.top_r if request.top_r is not None else HARDWARE_RERANK_MODEL_CONFIG["default_params"]["top_r"]
            min_score = request.min_score if request.min_score is not None else HARDWARE_RERANK_MODEL_CONFIG["default_params"]["min_score"]
        history = request.history
        # RAG问答只支持流式输出
        async def generate_stream():
            stream_start_time = time.time()
            try:
                logger.info(f"开始RAG流式生成", request_id=msg_id)
                async for chunk in qa.generate_stream(
                    query=request.query,
                    user_id=request.user_id,
                    history=history,
                    top_k=top_k,
                    top_r=top_r,
                    min_score=min_score,
                    timeout=600,
                    conversation_id=conversation_id,
                    enable_thinking=request.enable_thinking,
                    mode=request.mode,
                    temperature=request.temperature,
                    top_p=request.top_p
                ):
                    if isinstance(chunk, dict):
                        chunk["msg_id"] = msg_id
                        chunk["conversation_id"] = conversation_id
                    yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
                stream_end_time = time.time()
                logger.info(f"RAG流式问答完成，耗时: {stream_end_time - stream_start_time:.2f}秒", request_id=msg_id)
            except Exception as e:
                stream_end_time = time.time()
                logger.error(f"RAG流式生成错误: {str(e)}，耗时: {stream_end_time - stream_start_time:.2f}秒", request_id=msg_id)
                error_json = ErrorResponse(
                    success=False,
                    message=ERROR_CODES["MODEL_ERROR"],
                    error_code="MODEL_ERROR",
                    error_detail={"error": str(e)}
                ).model_dump_json()
                yield f"data: {error_json}\n\n"
        return StreamingResponse(
            generate_stream(),
            media_type="text/event-stream"
        )
    except asyncio.TimeoutError as e:
        end_time = time.time()
        logger.error(f"RAG问答超时: {str(e)}，耗时: {end_time - start_time:.2f}秒", request_id=request.msg_id)
        raise HTTPException(
            status_code=504,
            detail=ErrorResponse(
                success=False,
                message="请求处理超时",
                error_code="TIMEOUT_ERROR",
                error_detail={"error": str(e)}
            ).model_dump()
        )
    except Exception as e:
        end_time = time.time()
        logger.error(f"RAG问答错误: {str(e)}，耗时: {end_time - start_time:.2f}秒", request_id=request.msg_id)
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                success=False,
                message=ERROR_CODES["INTERNAL_ERROR"],
                error_code="INTERNAL_ERROR",
                error_detail={"error": str(e)}
            ).model_dump()
        )

# DATAQA问答API
@router.post(
    "/data-qa", 
    response_model=DATAQAResponse,
    responses={
        200: {"model": DATAQAResponse, "description": "成功响应"},
        400: {"model": ErrorResponse, "description": "请求参数错误"},
        500: {"model": ErrorResponse, "description": "服务器内部错误"},
        504: {"model": ErrorResponse, "description": "请求处理超时"}
    },
    dependencies=[Depends(verify_api_token)]
)
async def data_qa(request: DATAQARequest):
    """DATAQA问答接口"""
    start_time = time.time()
    logger.info("DATAQA问答请求参数：{}", str(request), request_id=request.msg_id)
    try:
        if not request.msg_id or not request.conversation_id or request.history is None:
            raise HTTPException(
                status_code=400,
                detail=ErrorResponse(
                    success=False,
                    message="msg_id、conversation_id、history为必填参数",
                    error_code="INVALID_REQUEST",
                    error_detail={}
                ).model_dump()
            )
        msg_id = request.msg_id
        conversation_id = request.conversation_id
        logger.info("DATAQA问答请求开始: user_id={} | conv_id={} | query={}", request.user_id, request.conversation_id, request.query, request_id=msg_id)
        qa = DATAQA(model_id=request.model_id, request_id=msg_id)
        history = request.history
        # 当用户不传top_k参数时，使用配置文件中的默认值
        top_k = request.top_k if request.top_k is not None else DATA_SEARCH_MODEL_CONFIG["default_params"]["top_k"]
        # 当用户不传top_r参数时，使用配置文件中的默认值
        top_r = request.top_r if request.top_r is not None else DATA_RERANK_MODEL_CONFIG["default_params"]["top_r"]
        # 当用户不传min_score参数时，使用配置文件中的默认值
        min_score = request.min_score if request.min_score is not None else DATA_RERANK_MODEL_CONFIG["default_params"]["min_score"]
        if request.stream:
            async def generate_stream():
                stream_start_time = time.time()
                try:
                    logger.info(f"开始DATAQA流式生成", request_id=msg_id)
                    # full_reasonint_content = ""
                    # full_content = ""
                    async for chunk in qa.generate_stream(
                        query=request.query,
                        user_id=request.user_id,
                        history=history,
                        top_k=top_k,
                        top_r=top_r,
                        min_score=min_score,
                        timeout=600,
                        conversation_id=conversation_id,
                        enable_thinking=request.enable_thinking,
                        mode=request.mode,
                        temperature=request.temperature,
                        top_p=request.top_p
                    ):
                        if isinstance(chunk, dict):
                            # if chunk["type"] == "reasoning":
                            #     full_reasonint_content += chunk["content"]
                            # if chunk["type"] == "content":
                            #     full_content += chunk["content"]
                            chunk["msg_id"] = msg_id
                            chunk["conversation_id"] = conversation_id
                        yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
                    # yield f"data: {json.dumps(full_reasonint_content, ensure_ascii=False)}\n\n"
                    # yield f"data: {json.dumps(full_content, ensure_ascii=False)}\n\n"
                    # print(f"full_reasonint_content: {full_reasonint_content}")
                    # print(f"full_content: {full_content}")
                    stream_end_time = time.time()
                    logger.info(f"DATAQA流式问答完成，耗时: {stream_end_time - stream_start_time:.2f}秒", request_id=msg_id)
                except Exception as e:
                    stream_end_time = time.time()
                    logger.error(f"DATAQA流式生成错误: {str(e)}，耗时: {stream_end_time - stream_start_time:.2f}秒", request_id=msg_id)
                    error_json = ErrorResponse(
                        success=False,
                        message=ERROR_CODES["MODEL_ERROR"],
                        error_code="MODEL_ERROR",
                        error_detail={"error": str(e)}
                    ).model_dump_json()
                    yield f"data: {error_json}\n\n"
            return StreamingResponse(
                generate_stream(),
                media_type="text/event-stream"
            )
        logger.info(f"开始生成DATAQA回答", request_id=msg_id)
        result = await qa.generate(
            query=request.query,
            user_id=request.user_id,
            history=history,
            top_k=top_k,
            top_r=top_r,
            min_score=min_score,
            timeout=600,
            conversation_id=conversation_id,
            enable_thinking=request.enable_thinking,
            mode=request.mode,
            temperature=request.temperature,
            top_p=request.top_p
        )
        end_time = time.time()
        logger.info(f"DATAQA问答完成，耗时: {end_time - start_time:.2f}秒", request_id=msg_id)
        return DATAQAResponse(
            answer=result["content"],
            model_id=request.model_id,
            msg_id=msg_id,
            conversation_id=conversation_id,
            references=result.get("references")
        )
    except asyncio.TimeoutError as e:
        end_time = time.time()
        logger.error(f"DATAQA问答超时: {str(e)}，耗时: {end_time - start_time:.2f}秒", request_id=request.msg_id)
        raise HTTPException(
            status_code=504,
            detail=ErrorResponse(
                success=False,
                message="请求处理超时",
                error_code="TIMEOUT_ERROR",
                error_detail={"error": str(e)}
            ).model_dump()
        )
    except Exception as e:
        end_time = time.time()
        logger.error(f"DATAQA问答错误: {str(e)}，耗时: {end_time - start_time:.2f}秒", request_id=request.msg_id)
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                success=False,
                message=ERROR_CODES["INTERNAL_ERROR"],
                error_code="INTERNAL_ERROR",
                error_detail={"error": str(e)}
            ).model_dump()
        )

# ISCQA问答API
@router.post(
    "/isc-qa",
    response_model=ISCQAResponse,
    responses={
        200: {"model": ISCQAResponse, "description": "成功响应"},
        400: {"model": ErrorResponse, "description": "请求参数错误"},
        500: {"model": ErrorResponse, "description": "服务器内部错误"},
        504: {"model": ErrorResponse, "description": "请求处理超时"}
    },
    dependencies=[Depends(verify_api_token)]
)
async def isc_qa(request: ISCQARequest):
    """ISCQA问答接口"""
    start_time = time.time()
    logger.info("ISCQA问答请求参数：{}", str(request), request_id=request.msg_id)
    try:
        if not request.msg_id or not request.conversation_id or request.history is None:
            raise HTTPException(
                status_code=400,
                detail=ErrorResponse(
                    success=False,
                    message="msg_id、conversation_id、history为必填参数",
                    error_code="INVALID_REQUEST",
                    error_detail={}
                ).model_dump()
            )
        msg_id = request.msg_id
        conversation_id = request.conversation_id
        logger.info("ISCQA问答请求开始: user_id={} | conv_id={} | query={}", request.user_id, request.conversation_id, request.query, request_id=msg_id)
        qa = ISCQA(model_id=request.model_id, request_id=msg_id)
        history = request.history
        # 当用户不传top_k参数时，使用配置文件中的默认值
        top_k = request.top_k if request.top_k is not None else ISC_SEARCH_MODEL_CONFIG["default_params"]["top_k"]
        # 当用户不传top_r参数时，使用配置文件中的默认值
        top_r = request.top_r if request.top_r is not None else ISC_RERANK_MODEL_CONFIG["default_params"]["top_r"]
        # 当用户不传min_score参数时，使用配置文件中的默认值
        min_score = request.min_score if request.min_score is not None else ISC_RERANK_MODEL_CONFIG["default_params"]["min_score"]
        if request.stream:
            async def generate_stream():
                stream_start_time = time.time()
                try:
                    logger.info(f"开始ISCQA流式生成", request_id=msg_id)
                    async for chunk in qa.generate_stream(
                        query=request.query,
                        user_id=request.user_id,
                        history=history,
                        top_k=top_k,
                        top_r=top_r,
                        min_score=min_score,
                        timeout=600,
                        conversation_id=conversation_id,
                        enable_thinking=request.enable_thinking,
                        mode=request.mode,
                        temperature=request.temperature,
                        top_p=request.top_p
                    ):
                        if isinstance(chunk, dict):
                            chunk["msg_id"] = msg_id
                            chunk["conversation_id"] = conversation_id
                        yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
                    stream_end_time = time.time()
                    logger.info(f"ISCQA流式问答完成，耗时: {stream_end_time - stream_start_time:.2f}秒", request_id=msg_id)
                except Exception as e:
                    stream_end_time = time.time()
                    logger.error(f"ISCQA流式生成错误: {str(e)}，耗时: {stream_end_time - stream_start_time:.2f}秒", request_id=msg_id)
                    error_json = ErrorResponse(
                        success=False,
                        message=ERROR_CODES["MODEL_ERROR"],
                        error_code="MODEL_ERROR",
                        error_detail={"error": str(e)}
                    ).model_dump_json()
                    yield f"data: {error_json}\n\n"
            return StreamingResponse(
                generate_stream(),
                media_type="text/event-stream"
            )
        logger.info(f"开始生成ISCQA回答", request_id=msg_id)
        result = await qa.generate(
            query=request.query,
            user_id=request.user_id,
            history=history,
            top_k=top_k,
            top_r=top_r,
            min_score=min_score,
            timeout=600,
            conversation_id=conversation_id,
            enable_thinking=request.enable_thinking,
            mode=request.mode,
            temperature=request.temperature,
            top_p=request.top_p
        )
        end_time = time.time()
        logger.info(f"ISCQA问答完成，耗时: {end_time - start_time:.2f}秒", request_id=msg_id)
        return ISCQAResponse(
            answer=result["content"],
            model_id=request.model_id,
            msg_id=msg_id,
            conversation_id=conversation_id,
            references=result.get("references")
        )
    except asyncio.TimeoutError as e:
        end_time = time.time()
        logger.error(f"ISCQA问答超时: {str(e)}，耗时: {end_time - start_time:.2f}秒", request_id=request.msg_id)
        raise HTTPException(
            status_code=504,
            detail=ErrorResponse(
                success=False,
                message="请求处理超时",
                error_code="TIMEOUT_ERROR",
                error_detail={"error": str(e)}
            ).model_dump()
        )
    except Exception as e:
        end_time = time.time()
        logger.error(f"ISCQA问答错误: {str(e)}，耗时: {end_time - start_time:.2f}秒", request_id=request.msg_id)
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                success=False,
                message=ERROR_CODES["INTERNAL_ERROR"],
                error_code="INTERNAL_ERROR",
                error_detail={"error": str(e)}
            ).model_dump()
        )

# 文档问答API
@router.post(
    "/doc-qa", 
    response_model=DocQAResponse,
    responses={
        200: {"model": DocQAResponse, "description": "成功响应"},
        400: {"model": ErrorResponse, "description": "请求参数错误"},
        500: {"model": ErrorResponse, "description": "服务器内部错误"},
        504: {"model": ErrorResponse, "description": "请求处理超时"}
    },
    dependencies=[Depends(verify_api_token)]
)
async def doc_qa(request: DocQARequest):
    """文档问答接口"""
    start_time = time.time()
    logger.info("文档问答请求参数：{}", str(request), request_id=request.msg_id)
    try:
        if not request.msg_id or not request.conversation_id or request.history is None:
            raise HTTPException(
                status_code=400,
                detail=ErrorResponse(
                    success=False,
                    message="msg_id、conversation_id、history为必填参数",
                    error_code="INVALID_REQUEST",
                    error_detail={}
                ).model_dump()
            )
        msg_id = request.msg_id
        conversation_id = request.conversation_id
        logger.info("文档问答请求开始: user_id={} | conv_id={} | query={}", request.user_id, request.conversation_id, request.query, request_id=msg_id)
        qa = DocQA(model_id=request.model_id, request_id=msg_id)
        history = request.history
        if request.stream:
            async def generate_stream():
                stream_start_time = time.time()
                try:
                    logger.info(f"开始文档流式生成", request_id=msg_id)
                    # full_reasonint_content = ""
                    # full_content = ""
                    async for chunk in qa.generate_stream(
                        query=request.query,
                        user_id=request.user_id,
                        history=history,
                        timeout=600,
                        conversation_id=conversation_id,
                        enable_thinking=request.enable_thinking,
                        mode=request.mode
                    ):
                        if isinstance(chunk, dict):
                            chunk["msg_id"] = msg_id
                            chunk["conversation_id"] = conversation_id
                        yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
                    stream_end_time = time.time()
                    logger.info(f"文档流式问答完成，耗时: {stream_end_time - stream_start_time:.2f}秒", request_id=msg_id)
                except Exception as e:
                    stream_end_time = time.time()
                    logger.error(f"文档流式生成错误: {str(e)}，耗时: {stream_end_time - stream_start_time:.2f}秒", request_id=msg_id)
                    error_json = ErrorResponse(
                        success=False,
                        message=ERROR_CODES["MODEL_ERROR"],
                        error_code="MODEL_ERROR",
                        error_detail={"error": str(e)}
                    ).model_dump_json()
                    yield f"data: {error_json}\n\n"
            return StreamingResponse(
                generate_stream(),
                media_type="text/event-stream"
            )
        logger.info(f"开始生成文档回答", request_id=msg_id)
        result = await qa.generate(
            query=request.query,
            user_id=request.user_id,
            history=history,
            timeout=600,
            conversation_id=conversation_id,
            enable_thinking=request.enable_thinking
        )
        end_time = time.time()
        logger.info(f"文档问答完成，耗时: {end_time - start_time:.2f}秒", request_id=msg_id)
        return DocQAResponse(
            answer=result["content"],
            model_id=request.model_id,
            msg_id=msg_id,
            conversation_id=conversation_id,
            references=result.get("references")
        )
    except asyncio.TimeoutError as e:
        end_time = time.time()
        logger.error(f"文档问答超时: {str(e)}，耗时: {end_time - start_time:.2f}秒", request_id=request.msg_id)
        raise HTTPException(
            status_code=504,
            detail=ErrorResponse(
                success=False,
                message="请求处理超时",
                error_code="TIMEOUT_ERROR",
                error_detail={"error": str(e)}
            ).model_dump()
        )
    except Exception as e:
        end_time = time.time()
        logger.error(f"文档问答错误: {str(e)}，耗时: {end_time - start_time:.2f}秒", request_id=request.msg_id)
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                success=False,
                message=ERROR_CODES["INTERNAL_ERROR"],
                error_code="INTERNAL_ERROR",
                error_detail={"error": str(e)}
            ).model_dump()
        )


# 健康检查API
    
# 定义健康检查响应模型（标准化返回结构）
class HealthCheckResponse(BaseModel):
    status: str
    timestamp: str
    # 可扩展的组件状态（如数据库、缓存等）
    components: dict = {}

@router.get(
    "/",
    summary="服务健康检查（根路径）",
    description="返回服务基础状态，适合负载均衡器探活",
    response_model=HealthCheckResponse,
)
async def health_check_root():
    try:
        response = {
            "status": "OK",
            "timestamp": datetime.now().isoformat(),
            "components": {"base": "available"},  # 基础状态标识
        }
        return response
    except Exception as e:
        logger.error(f"根路径健康检查失败: {str(e)}")
        raise HTTPException(status_code=503, detail=f"服务异常：{str(e)}")


@router.get(
    "/health",
    summary="详细健康检查",
    description="返回服务及依赖组件的详细健康状态",
    response_model=HealthCheckResponse,
)
async def health_check_detailed():
    try:
        # 可扩展的检查项（参考网页1的异步检查逻辑）
        status = {
            "status": "ok",
            "timestamp": datetime.now().isoformat(),
            "components": {
                "database": "available",  # 示例：实际可添加数据库连接检查
                "cache": "available",  # 示例：缓存服务检查
            },
        }
        return status
    except Exception as e:
        logger.error(f"详细健康检查失败: {str(e)}")
        raise HTTPException(status_code=503, detail=f"服务异常：{str(e)}")

# SEARCH API
@router.post(
    "/search",
    response_model=SearchResponse,
    responses={
        200: {"model": SearchResponse, "description": "成功响应"},
        400: {"model": ErrorResponse, "description": "请求参数错误"},
        500: {"model": ErrorResponse, "description": "服务器内部错误"},
        504: {"model": ErrorResponse, "description": "请求处理超时"}
    },
    dependencies=[Depends(verify_api_token)]
)
async def search(request: SearchRequest):
    """搜索接口"""
    start_time = time.time()
    logger.info("搜索请求参数：{}", str(request), request_id=request.msg_id)
    try:
        msg_id = request.msg_id
        logger.info("搜索请求开始: user_id={} | query={}", request.user_id, request.query, request_id=msg_id)
        search_service = SEARCH(request_id=msg_id)

        # 当用户不传top_k参数时，使用配置文件中的默认值
        top_k = request.top_k if request.top_k is not None else ALL_SEARCH_MODEL_CONFIG["default_params"]["top_k"]
        # 当用户不传min_score参数时，使用配置文件中的默认值
        top_r = request.top_r if request.top_r is not None else ALL_SEARCH_RERANK_CONFIG["default_params"]["top_r"]
        min_score = request.min_score if request.min_score is not None else ALL_SEARCH_RERANK_CONFIG["default_params"]["min_score"]

        async def generate_stream():
            stream_start_time = time.time()
            try:
                logger.info(f"开始搜索流式生成", request_id=msg_id)
                async for chunk in search_service.search_all_collections(
                    query=request.query,
                    user_id=request.user_id,
                    top_k=top_k,
                    top_r=top_r,
                    timeout=600,
                    min_score=min_score,
                    collections=request.collections
                ):
                    if isinstance(chunk, dict):
                        chunk["msg_id"] = msg_id
                    yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
                stream_end_time = time.time()
                logger.info(f"搜索完成，耗时: {stream_end_time - stream_start_time:.2f}秒", request_id=msg_id)
            except Exception as e:
                stream_end_time = time.time()
                logger.error(f"搜索生成错误: {str(e)}，耗时: {stream_end_time - stream_start_time:.2f}秒", request_id=msg_id)
                error_json = ErrorResponse(
                    success=False,
                    message=ERROR_CODES["SEARCH_ERROR"],
                    error_code="SEARCH_ERROR",
                    error_detail={"error": str(e)}
                ).model_dump_json()
                yield f"data: {error_json}\n\n"
        return StreamingResponse(
            generate_stream(),
            media_type="text/event-stream"
        )
    except asyncio.TimeoutError as e:
        end_time = time.time()
        logger.error(f"搜索超时: {str(e)}，耗时: {end_time - start_time:.2f}秒", request_id=request.msg_id)
        raise HTTPException(
            status_code=504,
            detail=ErrorResponse(
                success=False,
                message="请求处理超时",
                error_code="TIMEOUT_ERROR",
                error_detail={"error": str(e)}
            ).model_dump()
        )
    except Exception as e:
        end_time = time.time()
        logger.error(f"搜索错误: {str(e)}，耗时: {end_time - start_time:.2f}秒", request_id=request.msg_id)
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                success=False,
                message=ERROR_CODES["INTERNAL_ERROR"],
                error_code="INTERNAL_ERROR",
                error_detail={"error": str(e)}
            ).model_dump()
        )

# ALLQA问答API
@router.post(
    "/all-qa",
    response_model=ALLQAResponse,
    responses={
        200: {"model": ALLQAResponse, "description": "成功响应"},
        400: {"model": ErrorResponse, "description": "请求参数错误"},
        500: {"model": ErrorResponse, "description": "服务器内部错误"},
        504: {"model": ErrorResponse, "description": "请求处理超时"}
    },
    dependencies=[Depends(verify_api_token)]
)
async def all_qa(request: ALLQARequest):
    """ALLQA问答接口"""
    start_time = time.time()
    logger.info("ALLQA问答请求参数：{}", str(request), request_id=request.msg_id)
    try:
        if not request.msg_id or not request.conversation_id or request.history is None:
            raise HTTPException(
                status_code=400,
                detail=ErrorResponse(
                    success=False,
                    message="msg_id、conversation_id、history为必填参数",
                    error_code="INVALID_REQUEST",
                    error_detail={}
                ).model_dump()
            )
        msg_id = request.msg_id
        conversation_id = request.conversation_id
        logger.info("ALLQA问答请求开始: user_id={} | conv_id={} | query={}", request.user_id, request.conversation_id, request.query, request_id=msg_id)
        qa = ALLQA(model_id=request.model_id, request_id=msg_id)
        history = request.history
        # 当用户不传top_k参数时，使用配置文件中的默认值
        top_k = request.top_k if request.top_k is not None else ALL_QA_SEARCH_CONFIG["default_params"]["top_k"]
        # 当用户不传top_r参数时，使用配置文件中的默认值
        top_r = request.top_r if request.top_r is not None else ALL_QA_RERANK_CONFIG["default_params"]["top_r"]
        # 当用户不传min_score参数时，使用配置文件中的默认值
        min_score = request.min_score if request.min_score is not None else ALL_QA_RERANK_CONFIG["default_params"]["min_score"]

        # ALLQA问答只支持流式输出
        async def generate_stream():
            stream_start_time = time.time()
            try:
                logger.info(f"开始ALLQA流式生成", request_id=msg_id)
                async for chunk in qa.generate_stream(
                    query=request.query,
                    user_id=request.user_id,
                    history=history,
                    top_k=top_k,
                    top_r=top_r,
                    min_score=min_score,
                    timeout=600,
                    conversation_id=conversation_id,
                    enable_thinking=request.enable_thinking,
                    mode=request.mode,
                    temperature=request.temperature,
                    top_p=request.top_p,
                    collection=request.collection
                ):
                    if isinstance(chunk, dict):
                        chunk["msg_id"] = msg_id
                        chunk["conversation_id"] = conversation_id
                    yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
                stream_end_time = time.time()
                logger.info(f"ALLQA流式问答完成，耗时: {stream_end_time - stream_start_time:.2f}秒", request_id=msg_id)
            except Exception as e:
                stream_end_time = time.time()
                logger.error(f"ALLQA流式生成错误: {str(e)}，耗时: {stream_end_time - stream_start_time:.2f}秒", request_id=msg_id)
                error_json = ErrorResponse(
                    success=False,
                    message=ERROR_CODES["MODEL_ERROR"],
                    error_code="MODEL_ERROR",
                    error_detail={"error": str(e)}
                ).model_dump_json()
                yield f"data: {error_json}\n\n"
        return StreamingResponse(
            generate_stream(),
            media_type="text/event-stream"
        )
    except asyncio.TimeoutError as e:
        end_time = time.time()
        logger.error(f"ALLQA问答超时: {str(e)}，耗时: {end_time - start_time:.2f}秒", request_id=request.msg_id)
        raise HTTPException(
            status_code=504,
            detail=ErrorResponse(
                success=False,
                message="请求处理超时",
                error_code="TIMEOUT_ERROR",
                error_detail={"error": str(e)}
            ).model_dump()
        )
    except Exception as e:
        end_time = time.time()
        logger.error(f"ALLQA问答错误: {str(e)}，耗时: {end_time - start_time:.2f}秒", request_id=request.msg_id)
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                success=False,
                message=ERROR_CODES["INTERNAL_ERROR"],
                error_code="INTERNAL_ERROR",
                error_detail={"error": str(e)}
            ).model_dump()
        )
