#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试联网搜索功能
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.websearch_service import get_websearch_service

async def test_websearch():
    """测试联网搜索功能"""
    print("=== 测试联网搜索服务 ===")
    
    # 获取websearch服务
    service = get_websearch_service()
    
    # 测试查询
    test_query = "刘德华是谁"
    print(f"测试查询: {test_query}")
    
    try:
        # 执行搜索
        result = await service.search(test_query, "test_user")
        
        print(f"\n搜索结果状态: {'成功' if 'webPages' in result else '失败'}")
        
        if 'webPages' in result:
            web_pages = result['webPages']
            if 'value' in web_pages:
                pages = web_pages['value']
                print(f"获取到 {len(pages)} 个搜索结果")
                
                # 提取summaries
                summaries = service.extract_summaries(result)
                print(f"提取到 {len(summaries)} 个summary")
                
                # 格式化搜索上下文
                context = service.format_search_context(summaries)
                print(f"\n格式化上下文长度: {len(context)}")
                print(f"上下文预览:\n{context[:300]}...")
                
                return True
            else:
                print("搜索结果中没有找到value字段")
                return False
        else:
            print(f"搜索失败: {result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"测试异常: {str(e)}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_websearch())
    if success:
        print("\n✅ 联网搜索功能测试通过")
    else:
        print("\n❌ 联网搜索功能测试失败")
