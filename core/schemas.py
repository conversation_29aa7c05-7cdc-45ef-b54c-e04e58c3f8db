"""API请求和响应模型定义"""

from pydantic import BaseModel
from typing import List, Dict, Any, Optional

# 基础响应模型
class BaseResponse(BaseModel):
    """基础响应模型"""
    success: bool = True
    message: str = "操作成功"

# 错误响应模型
class ErrorResponse(BaseModel):
    """错误响应模型"""
    success: bool = False
    message: str
    error_code: str
    error_detail: Dict[str, Any] = {}

# 对话历史记录项
class HistoryItem(BaseModel):
    """对话历史记录项"""
    query: str
    content: str

# LLM问答请求
class LLMQARequest(BaseModel):
    """LLM问答请求模型"""
    query: str
    user_id: str
    model_id: str
    msg_id: str
    conversation_id: str
    history: List[Dict[str, str]]  # [{"query":..., "content":...}]
    stream: bool = True
    enable_thinking: bool = True
    enable_web_search: bool = False

# LLM问答响应
class LLMQAResponse(BaseResponse):
    """LLM问答响应模型"""
    answer: str
    model_id: str
    msg_id: str
    conversation_id: Optional[str] = None

# 参考文档
class Reference(BaseModel):
    """参考文档模型"""
    title: str
    content: str
    score: float

# RAG问答请求
class RAGQARequest(BaseModel):
    """RAG问答请求模型"""
    query: str
    user_id: str
    model_id: str
    msg_id: str
    conversation_id: str
    history: List[Dict[str, str]]  # [{"query":..., "content":...}]
    stream: bool = True
    top_k: Optional[int] = None
    top_r: Optional[int] = None
    min_score: Optional[float] = None
    enable_thinking: bool = True
    mode: Optional[str] = "strict"  # 模式：strict(严格模式) 或 common(普通模式)，默认为普通模式
    temperature: Optional[float] = 0.3  # 温度参数，控制生成的随机性
    top_p: Optional[float] = None  # top_p参数，控制生成的多样性
    knowledge: Optional[str] = None  # 新增，支持 car/hardware

# RAG问答响应
class RAGQAResponse(BaseResponse):
    """RAG问答响应模型"""
    answer: str
    model_id: str
    msg_id: str
    conversation_id: Optional[str] = None
    references: Optional[List[Reference]] = None

# DATAQA问答请求
class DATAQARequest(BaseModel):
    """DATAQA问答请求模型"""
    query: str
    user_id: str
    model_id: str
    msg_id: str
    conversation_id: str
    history: List[Dict[str, str]]  # [{"query":..., "content":...}]
    stream: bool = True
    top_k: Optional[int] = None
    top_r: Optional[int] = None
    min_score: Optional[float] = None
    enable_thinking: bool = True
    mode: Optional[str] = "strict"  # 模式：strict(严格模式) 或 common(普通模式)，默认为严格模式
    temperature: Optional[float] = None  # 温度参数，控制生成的随机性
    top_p: Optional[float] = None  # top_p参数，控制生成的多样性

# DATAQA问答响应
class DATAQAResponse(BaseResponse):
    """DATAQA问答响应模型"""
    answer: str
    model_id: str
    msg_id: str
    conversation_id: Optional[str] = None
    references: Optional[List[Reference]] = None

# ISCQA问答请求
class ISCQARequest(BaseModel):
    """ISCQA问答请求模型"""
    query: str
    user_id: str
    model_id: str
    msg_id: str
    conversation_id: str
    history: List[Dict[str, str]]  # [{"query":..., "content":...}]
    stream: bool = True
    top_k: Optional[int] = None
    top_r: Optional[int] = None
    min_score: Optional[float] = None
    enable_thinking: bool = True
    mode: Optional[str] = "strict"  # 模式：strict(严格模式) 或 common(普通模式)，默认为严格模式
    temperature: Optional[float] = None  # 温度参数，控制生成的随机性
    top_p: Optional[float] = None  # top_p参数，控制生成的多样性

# ISCQA问答响应
class ISCQAResponse(BaseResponse):
    """ISCQA问答响应模型"""
    answer: str
    model_id: str
    msg_id: str
    conversation_id: Optional[str] = None
    references: Optional[List[Reference]] = None

# 文档问答请求
class DocQARequest(BaseModel):
    """DocQA问答请求模型"""
    query: str
    user_id: str
    model_id: str
    msg_id: str
    conversation_id: str
    history: List[Dict[str, str]]  # [{"query":..., "content":...}]
    stream: bool = True
    top_k: Optional[int] = None
    top_r: Optional[int] = None
    min_score: Optional[float] = None
    enable_thinking: bool = True
    mode: Optional[str] = "strict"  # 模式：strict(严格模式) 或 common(普通模式)，默认为严格模式

# 文档问答响应
class DocQAResponse(BaseResponse):
    """DocQA问答响应模型"""
    answer: str
    model_id: str
    msg_id: str
    conversation_id: Optional[str] = None
    references: Optional[List[Reference]] = None

# 搜索结果项
class SearchResult(BaseModel):
    """搜索结果项模型"""
    collection: str
    refs: str  # JSON字符串格式的引用数据

# SEARCH请求
class SearchRequest(BaseModel):
    """SEARCH请求模型"""
    query: str
    user_id: str
    msg_id: Optional[str] = "search_uuid"
    top_k: Optional[int] = None
    top_r: Optional[int] = None
    min_score: Optional[float] = None
    collections: Optional[List[str]] = None  # 要检索的集合类型列表，如 ["car", "hardware"] 或 ["data"]

# SEARCH响应
class SearchResponse(BaseResponse):
    """SEARCH响应模型"""
    results: List[SearchResult]
    msg_id: str

# ALLQA问答请求
class ALLQARequest(BaseModel):
    """ALLQA问答请求模型"""
    query: str
    user_id: str
    model_id: str
    msg_id: str
    conversation_id: str
    history: List[Dict[str, str]]  # [{"query":..., "content":...}]
    stream: bool = True
    top_k: Optional[int] = None
    top_r: Optional[int] = None
    min_score: Optional[float] = None
    enable_thinking: bool = True
    mode: Optional[str] = "strict"  # 模式：strict(严格模式) 或 common(普通模式)，默认为严格模式
    temperature: Optional[float] = None  # 温度参数，控制生成的随机性
    top_p: Optional[float] = None  # top_p参数，控制生成的多样性
    collection: Optional[List[str]] = None  # 指定搜索的集合列表，如["car", "hardware"]

# ALLQA问答响应
class ALLQAResponse(BaseResponse):
    """ALLQA问答响应模型"""
    answer: str
    model_id: str
    msg_id: str
    conversation_id: Optional[str] = None
    references: Optional[List[Reference]] = None