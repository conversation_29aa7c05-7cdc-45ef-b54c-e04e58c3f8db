
import json
import httpx
import logging
import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 添加环境变量加载代码
from dotenv import load_dotenv

# 获取环境变量中的环境类型，默认为local
env_type = os.environ.get("ENVIRONMENT", "local")
env_file = f".env.{env_type}"

# 尝试加载对应环境的配置文件
env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), env_file)
if os.path.exists(env_path):
    load_dotenv(env_path)
    print(f"已加载{env_type}环境配置: {env_path}")
else:
    # 如果特定环境配置不存在，尝试加载默认.env文件
    default_env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
    if os.path.exists(default_env_path):
        load_dotenv(default_env_path)
        print(f"已加载默认环境配置: {default_env_path}")
    else:
        print(f"警告: 环境配置文件不存在: {env_path} 或 {default_env_path}")

from config.doc_redis_config import DOC_REDIS_CONFIG
from loguru import logger
from config.logging_config import configure_logging
configure_logging()


class RedisService():
    def __init__(self, config: dict = None, request_id: str = None):
        self.config = config or DOC_REDIS_CONFIG
        self.api_url = self.config["api_url"]
        self.logger = logger.bind(request_id=request_id)

    async def search(self, conversation_id: str, **kwargs):

        payload = {
            "conversationId": conversation_id,
        }
        self.logger.info(f"检索请求参数: {self.api_url}/conversation/attachment, 参数: {payload}")
        try:
            async with httpx.AsyncClient(timeout=60) as client:
                response = await client.post(
                    f"{self.api_url}/conversation/attachment",
                    json=payload,
                    headers={"Content-Type": "application/json"}
                )
            self.logger.info(f"检索响应: {response.status_code}")
            # print(f"{response.status_code} - {response.text}")
            if response.status_code == 200:
                result = response.json()
                search_results = result.get('data')
                return search_results, None
            else:
                self.logger.error(f"检索API请求失败: {response.status_code} - {response.text}")
                return None, f"API请求失败: {response.status_code} - {response.text}"
        except Exception as e:
            return None, str(e)


if __name__ == "__main__":
    import asyncio
    
    async def main():
        redis_service = RedisService(config={"api_url": "http://quickdb.test.b2c.srv/ai"}, request_id="2049560b-eaa0-46da-b6a7-3e4b8bf46616")
        result = await redis_service.search(conversation_id="d160e5a1-2d8a-4b56-b625-ecd6ae5a375e")
        print(json.dumps(result, indent=4, ensure_ascii=False))
    
    asyncio.run(main())