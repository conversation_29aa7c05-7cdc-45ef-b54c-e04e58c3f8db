import httpx
import logging
import json
from config.hardware_search_config import <PERSON><PERSON><PERSON><PERSON><PERSON>_RERANK_MODEL_CONFIG
from typing import List, Any
from loguru import logger
from config.logging_config import configure_logging
configure_logging()

class RerankService:
    def __init__(self, config=None, request_id: str = None):
        self.config = config or HARDWARE_RERANK_MODEL_CONFIG
        self.api_url = self.config["api_url"]
        self.api_key = self.config["api_key"]
        self.environment = self.config["environment"]
        self.default_params = self.config.get("default_params", {})
        self.top_r = self.config.get("top_r", 20)
        self.min_score = self.config.get("min_score", 0.5)
        self.logger = logger.bind(request_id=request_id)

    async def rerank_with_prompt_se(self, query, documents, instruction, top_r=None, min_score=None):
        """使用自定义prompt进行重排"""
        if not documents:
            return []
        payload = {
            "queries": [query] * len(documents),
            "documents": [doc.get("content", "") for doc in documents],
            "instruction": instruction
        }

        headers = {
            "api_key": self.api_key,
            "Content-Type": "application/json"
        }
        self.logger.info(f"Reranker API 调用: {self.api_url}")
        self.logger.info(f"reranker参数：top_r: {top_r}, min_score: {min_score}, 知识条数：{len(documents)}, 知识字数：{len(str(documents))}")
        try:
            async with httpx.AsyncClient(timeout=60) as client:
                response = await client.post(self.api_url, json=payload, headers=headers)
                response.raise_for_status()
                result = response.json()
                
            # print(f"response.status_code: {response.status_code}")
            # print(f"result: {result}")
            
            self.logger.info(f"Rerank 结果: {result}")
            scores = result.get("scores", [])
            scored_docs = list(zip(scores, documents))
            scored_docs.sort(key=lambda x: x[0], reverse=True)
            top_r = top_r if top_r else self.top_r
            min_score = min_score if min_score is not None else self.min_score
            # print("min_score:", min_score)
            # print("top_r:", top_r)
            if min_score > 0:
                scored_docs = [(score, doc) for score, doc in scored_docs if score >= min_score]
            if top_r is not None and top_r > 0:
                scored_docs = scored_docs[:int(top_r)]
            self.logger.info(f"Score小于{min_score}的文档被过滤掉，共过滤掉 {len(documents) - len(scored_docs)} 个文档")
            self.logger.info(f"Reranker 后处理成功，共获取到 {len(scored_docs)} 个文档")
            # print(f"reranked_retrieved_docs: {scored_docs}")
            reranked_retrieved_docs = [doc for score, doc in scored_docs]
            # print(f"reranked_retrieved_docs: {reranked_retrieved_docs}")
            format_reranked_retrieved_docs = self.format_retrieved_docs(reranked_retrieved_docs)
            self.logger.info(f"Reranker结果格式化完成，共获取到 {len(format_reranked_retrieved_docs)} 个文档")
            # print(f"format_reranked_retrieved_docs: {format_reranked_retrieved_docs}")
            return format_reranked_retrieved_docs
        except Exception as e:
            self.logger.error(f"Reranker API调用失败: {e}")
            return []

    async def rerank_with_prompt_cl(self, query, documents, instruction, top_r=None, min_score=None):
        """使用自定义prompt进行重排"""
        if not documents:
            return []
        prefix = '<|im_start|>system\nJudge whether the Document meets the requirements based on the Query and the Instruct provided. Note that the answer can only be "yes" or "no".<|im_end|>\n<|im_start|>user\n'
        suffix = "<|im_end|>\n<|im_start|>assistant\n<think>\n\n</think>\n\n"
        query_template = "{prefix}<Instruct>: {instruction}\n<Query>: {query}\n"
        document_template = "<Document>: {doc}{suffix}"
        format_query = query_template.format(prefix=prefix, instruction=instruction, query=query)
        format_documents = [
        document_template.format(doc=doc, suffix=suffix) for doc in documents]

        payload = {
            "query": format_query,
            "documents": format_documents,
            "truncate_prompt_tokens": -1
        }

        headers = {
            "Content-Type": "application/json"
        }
        self.logger.info(f"Reranker API 调用: {self.api_url}")
        self.logger.info(f"reranker参数：query: {query}, top_r: {top_r}, min_score: {min_score}, 知识条数：{len(documents)}, 知识字数：{len(str(documents))}")
        try:
            async with httpx.AsyncClient(timeout=60) as client:
                response = await client.post(self.api_url, json=payload, headers=headers)
                response.raise_for_status()
                results = response.json()
                
            # print(f"response.status_code: {response.status_code}")
            # print(f"result: {result}")
            
            # self.logger.info(f"Rerank 结果: {results}")
            results = results.get("results", [])
            scores = [result.get("relevance_score", 0) for result in results]
            self.logger.info(f"Rerank 结果: query: {query}, scores: {scores}")
            scored_docs = list(zip(scores, documents))
            scored_docs.sort(key=lambda x: x[0], reverse=True)
            top_r = top_r if top_r else self.top_r
            min_score = min_score if min_score is not None else self.min_score
            # print("min_score:", min_score)
            # print("top_r:", top_r)
            if min_score > 0:
                scored_docs = [(score, doc) for score, doc in scored_docs if score >= min_score]
            if top_r is not None and top_r > 0:
                scored_docs = scored_docs[:int(top_r)]
            self.logger.info(f"Score小于{min_score}的文档被过滤掉，共过滤掉 {len(documents) - len(scored_docs)} 个文档")
            self.logger.info(f"Reranker 后处理成功，共获取到 {len(scored_docs)} 个文档")
            # print(f"reranked_retrieved_docs: {scored_docs}")
            reranked_retrieved_docs = [doc for score, doc in scored_docs]
            # print(f"reranked_retrieved_docs: {reranked_retrieved_docs}")
            format_reranked_retrieved_docs = self.format_retrieved_docs(reranked_retrieved_docs)
            self.logger.info(f"Reranker结果格式化完成，共获取到 {len(format_reranked_retrieved_docs)} 个文档")
            # print(f"format_reranked_retrieved_docs: {format_reranked_retrieved_docs}")
            return format_reranked_retrieved_docs
        except Exception as e:
            self.logger.error(f"Reranker API调用失败: {e}")
            return []

    async def rerank_with_prompt(self, query, documents, instruction, top_r=None, min_score=None):
        """
        对外统一重排接口，根据环境变量选择不同的重排实现
        """
        if self.environment == "cloudml":
            return await self.rerank_with_prompt_cl(query, documents, instruction, top_r=top_r, min_score=min_score)
        else:
            return await self.rerank_with_prompt_se(query, documents, instruction, top_r=top_r, min_score=min_score)

    def format_retrieved_docs(self, retrieved_docs: List[Any]) -> List[Any]:
            '''
            input retrieved_docs格式:
            [
                {
                    "content": "PI 仿真项经验总结及设计指导\n环路电感优化思路\n\n时刻关注电流的"返回路径"和"最小合围面积"即环路面积（Loop  Area）,减小环路电感的最直接方式就是减小环路面积  \n!\n!  \n3-1 MTK 环路电感分析  \n减小环路面积的方式：",
                    "metadata": {
                        "Header 1": "PI 仿真项经验总结及设计指导",
                        "Header 2": "环路电感优化思路",
                        "doc_name": "PI仿真项经验总结及设计指导",
                        "doc_type": "doc",
                        "publish_time": "2025-04-23 19:54:26",
                        "project_area": "011",
                        "doc_url": "https://xiaomi.f.mioffice.cn/docx/doxk4Flb2yoMvfV51WSEedury9g",
                        "tm": "2025-05-06 19:23:48",
                        "importance": 90
                    },
                    "relevance_score": 64.73157942295074,
                    "doc_name": "PI仿真项经验总结及设计指导",
                    "doc_url": "https://xiaomi.f.mioffice.cn/docx/doxk4Flb2yoMvfV51WSEedury9g"
                    }
                }
            ]
            输出：
            [
                {
                    title：查询到的标题; 
                    content：查询到的内容;
                    docName：引用的文档名称;
                    sheetName：文档中章节的名称;
                    docUrl：引用的文档链接
                }
            ]
            '''
            formated_retrieved_docs = []
            for doc in retrieved_docs:
                # print(f"doc: {doc}")
                data = {
                    "title": "",
                    "content": "",
                    "docName": "",
                    "docUrl": "",
                    "sheetName": "",
                    "owner": "",
                    "update_time": "",
                    "publish_time": "",
                    "doc_type": "",
                    "project_area": ""
                }
                docUrl1 = doc.get("metadata_json", {}).get("doc_url", "")
                docUrl2 = doc.get("metadata_json", {}).get("url", "")
                docUrl = docUrl1 if docUrl1 else docUrl2
                docUrl = str(docUrl)
                # print(f"docUrl: {docUrl}")
                title1 = doc.get("metadata_json", {}).get("doc_name", "")
                title2 = doc.get("metadata_json", {}).get("title", "")
                # print(f"title1: {title1}")
                # print(f"title2: {title2}")
                title = title1 if title1 else title2
                title = str(title)
                # print(f"title: {title}")
                data.update({"title": title})
                data.update({"content": doc.get("content", "")})
                data.update({"docName": title})
                data.update({"docUrl": docUrl})
                data.update({"owner": doc.get("metadata_json", {}).get("owner", "")})
                data.update({"update_time": doc.get("metadata_json", {}).get("update_time", "")})
                data.update({"publish_time": doc.get("metadata_json", {}).get("publish_time", "")})
                data.update({"project_area": doc.get("metadata_json", {}).get("project_area", "")})
                data.update({"doc_type": doc.get("metadata_json", {}).get("doc_type", "")})
                docType = doc.get("metadata_json", {}).get("doc_type", "")
                if "xiaomi.f.mioffice.cn"in docUrl and docType in ["doc", "sheet"]:
                    sheetName = doc.get("metadata_json", {}).get("Header 2", "")
                    data.update({"sheetName": sheetName})
                formated_retrieved_docs.append(data)
                # print(f"data: {data}")
            return formated_retrieved_docs